# PDF表格提取工具

一个强大的PDF、DOCX、DOC文件表格检测和提取工具，支持将提取的表格转换为HTML、Markdown和LaTeX格式。

## 功能特性

- 🔍 **多格式支持**: 支持PDF、DOCX、DOC文件
- 🤖 **AI驱动**: 使用DocLayout-YOLO模型进行精确的表格检测
- 🎯 **高精度**: 支持置信度阈值配置，确保检测质量
- 📊 **多种输出**: 支持HTML、Markdown、LaTeX格式输出
- ⚡ **批量处理**: 支持批量文件处理
- 🔧 **可配置**: 丰富的配置选项，满足不同需求
- 🧠 **LLM集成**: 内置大语言模型和嵌入向量客户端，支持智能文本处理

## 安装

### 使用pip安装

```bash
pip install pdf-table-extraction
```

### 从源码安装

```bash
git clone https://github.com/your-username/pdf-table-extraction.git
cd pdf-table-extraction
pip install -e .
```

### 安装可选依赖

```bash
# 安装YOLO模型支持
pip install pdf-table-extraction[yolo]

# 安装MinerU支持
pip install pdf-table-extraction[mineru]

# 安装文档处理支持
pip install pdf-table-extraction[document]

# 安装所有可选依赖
pip install pdf-table-extraction[all]

# 安装开发依赖
pip install pdf-table-extraction[dev]
```

## 快速开始

### 命令行使用

```bash
# 提取PDF文件中的表格
pdf-table-extract input.pdf output_dir

# 提取DOCX文件中的表格
pdf-table-extract input.docx output_dir

# 只处理指定页面
pdf-table-extract input.pdf output_dir --page 0

# 禁用HTML转换
pdf-table-extract input.pdf output_dir --no-html

# 指定输出格式
pdf-table-extract input.pdf output_dir --html-formats html markdown

# 只显示统计信息
pdf-table-extract input.pdf --stats-only
```

### Python API使用

```python
from src.main import DocumentTableExtractor

# 初始化提取器
extractor = DocumentTableExtractor(device="cuda", dpi=200)

# 提取表格
result = extractor.extract_tables_from_file("input.pdf", "output_dir")

# 查看结果
print(f"提取了 {len(result['extracted_files'])} 个表格")
```

### LLM客户端使用

```python
import asyncio
from src.llm import call_llm, call_llm_stream, call_embedding

async def example():
    # 调用LLM
    response = await call_llm("请介绍一下Python")
    print(response)

    # 流式调用LLM
    async for chunk in call_llm_stream("写一首诗"):
        print(chunk, end="")

    # 生成嵌入向量
    embeddings = await call_embedding(["文本1", "文本2"])
    print(f"生成了 {len(embeddings)} 个向量")

# 运行示例
asyncio.run(example())
```

## 配置

项目支持通过环境变量或配置文件进行配置。主要配置项包括：

- `DEFAULT_DEVICE`: 设备类型 (cpu/cuda)
- `DEFAULT_DPI`: 图像分辨率
- `TABLE_DETECTION_CONF_THRESHOLD`: 表格检测置信度阈值
- `OUTPUT_MAX_IMAGE_WIDTH`: 输出图像最大宽度

## 模型支持

- **DocLayout-YOLO**: 用于文档布局检测
- **SLANet+**: 用于表格结构识别
- **StructTable-InternVL2-1B**: 用于图像到HTML转换

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request！

## 更新日志

### v1.0.0
- 初始版本发布
- 支持PDF、DOCX、DOC文件表格提取
- 支持HTML、Markdown、LaTeX输出格式
