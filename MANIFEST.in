# 包含文档文件
include README.md
include LICENSE
include requirements.txt
include start.sh

# 包含配置文件
recursive-include src/config *.py
recursive-include src *.py

# 排除不需要的文件
exclude .gitignore
recursive-exclude * __pycache__
recursive-exclude * *.py[co]
recursive-exclude * *.so
recursive-exclude * .DS_Store

# 排除输出目录和模型目录
prune output
prune models
prune docs
prune .git
prune .idea

# 包含测试文件（如果存在）
recursive-include tests *.py

# 包含示例文件（如果存在）
recursive-include examples *.py *.md
