#!/bin/bash
export https_proxy=http://127.0.0.1:7890
export http_proxy=http://127.0.0.1:7890

# 激活 mineru 环境
source ~/miniconda3/etc/profile.d/conda.sh
conda activate mineru

# 设置工作目录和Python路径
cd /home/<USER>/by5-assess/pdf_table_extraction
export PYTHONPATH="/home/<USER>/by5-assess/pdf_table_extraction"

# 临时清除可能冲突的Python路径，只保留当前项目路径
#python -m src.main "./docs/SY_T205374.1-2006 固井作业规程第1部分常规固井.pdf" "output/SY_T205374.1-2006 固井作业规程第1部分常规固井" --max-size 2000 --html-formats markdown
python -c "
import sys
sys.path = ['/home/<USER>/by5-assess/pdf_table_extraction'] + [p for p in sys.path if 'mixrag' not in p and 'by5-assess-backend' not in p]
from src.main import main
sys.argv = ['main.py', './docs/EP12-1-2井固井工程设计.docx', 'output/EP12-1-2井固井工程设计', '--max-size', '2000', '--html-formats', 'markdown']
main()
"