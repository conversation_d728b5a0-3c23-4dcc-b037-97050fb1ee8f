#!/bin/bash
export https_proxy=http://127.0.0.1:7890
export http_proxy=http://127.0.0.1:7890

# 激活 mineru 环境
source ~/miniconda3/etc/profile.d/conda.sh
conda activate mineru

# 设置工作目录和Python路径
cd /home/<USER>/by5-assess/pdf_table_extraction

# 使用 python -c 方式启动，确保路径正确
#python -m src.main "./docs/SY_T205374.1-2006 固井作业规程第1部分常规固井.pdf" "output/SY_T205374.1-2006 固井作业规程第1部分常规固井" --max-size 2000 --html-formats markdown
python -c "
import sys
import os
# 确保当前目录在最前面
sys.path.insert(0, os.getcwd())
# 移除可能冲突的路径
sys.path = [p for p in sys.path if not any(x in p for x in ['mixrag', 'by5-assess-backend'])]
# 运行模块
import runpy
sys.argv = ['src.main', './docs/EP12-1-2井固井工程设计.docx', 'output/EP12-1-2井固井工程设计', '--max-size', '2000', '--html-formats', 'markdown']
runpy.run_module('src.main', run_name='__main__')
"