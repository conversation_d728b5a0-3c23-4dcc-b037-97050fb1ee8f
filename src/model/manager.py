import os
import logging

from src.config.settings import DEFAULT_DEVICE, MODEL_PATHS, TABLE_DETECTION

logger = logging.getLogger(__name__)


class ModelManager:
    """模型管理器"""

    def __init__(self, device: str = DEFAULT_DEVICE):
        """
        初始化模型管理器
        
        Args:
            device: 设备类型 ("cpu" 或 "cuda")，默认使用CUDA
        """
        self.device = device
        self.layout_model = None
        self.table_model = None

        # 检查模型可用性
        self.mineru_available = self._check_mineru_available()
        self.doclayout_yolo_available = self._check_doclayout_yolo_available()

        logger.info(f"Model availability check:")
        logger.info(f"  MinerU: {self.mineru_available}")
        logger.info(f"  DocLayout YOLO: {self.doclayout_yolo_available}")

    def init_layout_model(self) -> bool:
        """
        初始化布局检测模型

        Returns:
            是否成功初始化
        """
        if self.mineru_available:
            return self._init_mineru_model()
        elif self.doclayout_yolo_available:
            return self._init_doclayout_yolo_model()
        else:
            logger.warning("No layout detection model available.")
            return False

    def _init_mineru_model(self) -> bool:
        """
        初始化MinerU模型
        """
        try:
            from mineru.model.layout.doclayout_yolo import DocLayoutYOLOModel
            local_model_path = MODEL_PATHS["doclayout_yolo"]

            if os.path.exists(local_model_path):
                logger.info(f"Using local MinerU model: {local_model_path}")
                self.layout_model = DocLayoutYOLOModel(
                    weight=local_model_path,
                    device=self.device,
                    imgsz=TABLE_DETECTION["imgsz"],
                    conf=TABLE_DETECTION["conf_threshold"],
                    iou=TABLE_DETECTION["iou_threshold"]
                )
                return True
            else:
                logger.error(f"Local MinerU model not found: {local_model_path}")
                return False
        except ImportError:
            logger.warning("MinerU library not found. Cannot initialize MinerU model.")
            return False
        except Exception as e:
            logger.error(f"Error initializing MinerU model: {e}")
            return False

    def _init_doclayout_yolo_model(self) -> bool:
        """
        初始化原始DocLayout YOLO模型
        """
        try:
            from doclayout_yolo import YOLOv10
            model_path = "models/doclayout_yolo.pt" # This path seems hardcoded, should be in settings if it's a different model
            if os.path.exists(model_path):
                logger.info(f"Using local DocLayout YOLO model: {model_path}")
                self.layout_model = YOLOv10(model_path)
                if self.device == "cuda":
                    self.layout_model = self.layout_model.to(self.device)
                return True
            else:
                logger.error(f"Local DocLayout YOLO model not found: {model_path}")
                return False
        except ImportError:
            logger.warning("DocLayout YOLO library not found. Cannot initialize DocLayout YOLO model.")
            return False
        except Exception as e:
            logger.error(f"Error initializing DocLayout YOLO model: {e}")
            return False

    def init_table_model(self) -> bool:
        """
        初始化表格识别模型

        Returns:
            是否成功初始化
        """
        # Placeholder for actual table model initialization
        logger.info("Table model initialization placeholder.")
        return True

    def is_table_category(self, category_id: int) -> bool:
        """
        判断类别ID是否为表格
        
        Args:
            category_id: 类别ID
            
        Returns:
            是否为表格类别
        """
        if self.mineru_available:
            try:
                from mineru.utils.enum_class import CategoryId
                table_categories = [CategoryId.TableBody]
                return category_id in table_categories
            except ImportError:
                logger.warning("MinerU CategoryId not found. Falling back to default table categories.")
                return category_id in [4, 5] # Fallback if MinerU enum is not available
        else:
            return category_id in [4, 5]

    def has_layout_model(self) -> bool:
        """检查是否有布局模型"""
        return self.layout_model is not None

    def has_table_model(self) -> bool:
        """检查是否有表格模型"""
        return self.table_model is not None

    def get_layout_model(self):
        """获取布局模型"""
        return self.layout_model

    def get_table_model(self):
        """获取表格模型"""
        return self.table_model

    def is_mineru_available(self) -> bool:
        """检查MinerU是否可用"""
        return self.mineru_available

    def _check_mineru_available(self) -> bool:
        """
        检查MinerU是否可用
        """
        try:
            from mineru.model.layout.doclayout_yolo import DocLayoutYOLOModel
            return True
        except ImportError:
            return False

    def _check_doclayout_yolo_available(self) -> bool:
        """
        检查DocLayout YOLO是否可用
        """
        try:
            from doclayout_yolo import YOLOv10
            return True
        except ImportError:
            return False
