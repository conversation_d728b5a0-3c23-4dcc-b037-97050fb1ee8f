"""
图片转HTML模块
从 image_2_html 模块中提取的核心功能，用于将表格图片转换为HTML、LaTeX或Markdown格式
"""

import dataclasses
from enum import IntEnum, auto
from typing import List, Union

import torch
from PIL import Image
from torch import nn
from transformers import AutoModel, AutoTokenizer, AutoImageProcessor, AutoConfig


class SeparatorStyle(IntEnum):
    """分隔符样式"""
    ADD_COLON_SINGLE = auto()
    ADD_COLON_TWO = auto()
    ADD_COLON_SPACE_SINGLE = auto()
    NO_COLON_SINGLE = auto()
    NO_COLON_TWO = auto()
    ADD_NEW_LINE_SINGLE = auto()
    LLAMA2 = auto()
    CHATGLM = auto()
    CHATML = auto()
    CHATINTERN = auto()
    DOLLY = auto()
    RWKV = auto()
    PHOENIX = auto()
    ROBIN = auto()
    FALCON_CHAT = auto()
    CHATGLM3 = auto()
    INTERNVL_ZH = auto()
    MPT = auto()


@dataclasses.dataclass
class Conversation:
    """对话模板管理类"""
    name: str
    system_template: str = '{system_message}'
    system_message: str = ''
    roles: tuple = ('USER', 'ASSISTANT')
    messages: List[List[str]] = dataclasses.field(default_factory=list)
    offset: int = 0
    sep_style: SeparatorStyle = SeparatorStyle.ADD_COLON_SINGLE
    sep: str = '\n'
    sep2: str = None
    stop_str: Union[str, List[str]] = None
    stop_token_ids: List[int] = None

    def get_prompt(self) -> str:
        """获取生成提示"""
        system_prompt = self.system_template.format(system_message=self.system_message)

        if self.sep_style == SeparatorStyle.MPT:
            ret = system_prompt + self.sep
            for role, message in self.messages:
                if message:
                    if type(message) is tuple:
                        message, _, _ = message
                    ret += role + message + self.sep
                else:
                    ret += role
            return ret
        else:
            # 简化版本，只支持MPT样式
            ret = system_prompt + self.sep
            for role, message in self.messages:
                if message:
                    ret += role + ': ' + message + self.sep
                else:
                    ret += role + ':'
            return ret

    def append_message(self, role: str, message: str):
        """添加新消息"""
        self.messages.append([role, message])

    def copy(self):
        """复制对话模板"""
        return Conversation(
            name=self.name,
            system_template=self.system_template,
            system_message=self.system_message,
            roles=self.roles,
            messages=[[x, y] for x, y in self.messages],
            offset=self.offset,
            sep_style=self.sep_style,
            sep=self.sep,
            sep2=self.sep2,
            stop_str=self.stop_str,
            stop_token_ids=self.stop_token_ids,
        )


# 预定义的对话模板
HERMES_TEMPLATE = Conversation(
    name='Hermes-2',
    system_template='<|im_start|>system\n{system_message}',
    system_message='You are a Table Image to LaTeX/Markdown/HTML Code converter.',
    roles=('<|im_start|>user\n', '<|im_start|>assistant\n'),
    sep_style=SeparatorStyle.MPT,
    sep='<|im_end|>',
    stop_token_ids=[2, 6, 7, 8],
    stop_str='<|endoftext|>',
)

# 全局对话模板注册表
conv_templates = {
    'Hermes-2': HERMES_TEMPLATE
}


def get_conv_template(name: str) -> Conversation:
    """获取对话模板"""
    return conv_templates[name].copy()


class ImageToHTMLConverter(nn.Module):
    """图片转HTML转换器
    
    基于InternVL模型，支持将表格图片转换为HTML、LaTeX或Markdown格式
    """

    def __init__(self,
                 model_path: str,
                 max_new_tokens: int = 1024,
                 max_time: int = 30,
                 flash_attn: bool = True,
                 device: str = 'cuda',
                 **kwargs):
        """
        初始化转换器
        
        Args:
            model_path: 模型路径
            max_new_tokens: 最大生成token数
            max_time: 最大生成时间（秒）
            flash_attn: 是否使用flash attention
            device: 设备类型
        """
        super().__init__()
        self.model_path = model_path
        self.max_new_tokens = max_new_tokens
        self.max_generate_time = max_time
        self.flash_attn = flash_attn
        self.device = device

        # 初始化模型组件
        self._init_tokenizer(model_path)
        self._init_image_processor(model_path)
        self._init_model(model_path)

        # 输出格式模板
        self.prompt_template = {
            'latex': '<latex>',
            'html': '<html>',
            'markdown': '<markdown>',
        }

        # 支持的输出格式
        self.supported_output_format = ['latex', 'html', 'markdown']

    def _init_model(self, model_path: str):
        """初始化模型"""
        self.model = AutoModel.from_pretrained(
            model_path,
            trust_remote_code=True,
            torch_dtype=torch.bfloat16,
            low_cpu_mem_usage=True,
            use_flash_attn=self.flash_attn,
            force_download=True,
        )
        self.model.eval()
        if self.device == 'cuda' and torch.cuda.is_available():
            self.model = self.model.cuda()

    def _init_image_processor(self, image_processor_path: str):
        """初始化图像处理器"""
        self.image_processor = AutoImageProcessor.from_pretrained(
            image_processor_path,
            trust_remote_code=True,
            force_download=True,
        )

    def _init_tokenizer(self, tokenizer_path: str):
        """初始化分词器"""
        self.tokenizer = AutoTokenizer.from_pretrained(
            tokenizer_path,
            trust_remote_code=True,
            use_fast=False,
            force_download=True,
        )

        self.image_context_token = '<IMG_CONTEXT>'
        self.image_token_num = 256
        self.image_start_token = '<img>'
        self.image_end_token = '</img>'
        self.img_context_token_id = self.tokenizer.convert_tokens_to_ids(self.image_context_token)

    def _format_image_tokens(self, patch_num: int) -> str:
        """格式化图像token"""
        return f'{self.image_start_token}{self.image_context_token * self.image_token_num * patch_num}{self.image_end_token}'

    def _find_closest_aspect_ratio(self, aspect_ratio: float, target_ratios: list,
                                   width: int, height: int, image_size: int) -> tuple:
        """找到最接近的宽高比"""
        best_ratio_diff = float('inf')
        best_ratio = (1, 1)
        area = width * height

        for ratio in target_ratios:
            target_aspect_ratio = ratio[0] / ratio[1]
            ratio_diff = abs(aspect_ratio - target_aspect_ratio)
            if ratio_diff < best_ratio_diff:
                best_ratio_diff = ratio_diff
                best_ratio = ratio
            elif ratio_diff == best_ratio_diff:
                if area > 0.5 * image_size * image_size * ratio[0] * ratio[1]:
                    best_ratio = ratio
        return best_ratio

    def _dynamic_preprocess(self, image: Image.Image, min_num: int = 1, max_num: int = 12,
                            image_size: int = 448, use_thumbnail: bool = True) -> List[Image.Image]:
        """动态预处理图像"""
        orig_width, orig_height = image.size
        aspect_ratio = orig_width / orig_height

        # 计算目标宽高比
        target_ratios = set(
            (i, j) for n in range(min_num, max_num + 1)
            for i in range(1, n + 1) for j in range(1, n + 1)
            if i * j <= max_num and i * j >= min_num
        )
        target_ratios = sorted(target_ratios, key=lambda x: x[0] * x[1])

        # 找到最接近的宽高比
        target_aspect_ratio = self._find_closest_aspect_ratio(
            aspect_ratio, target_ratios, orig_width, orig_height, image_size
        )

        # 计算目标宽度和高度
        target_width = image_size * target_aspect_ratio[0]
        target_height = image_size * target_aspect_ratio[1]
        blocks = target_aspect_ratio[0] * target_aspect_ratio[1]

        # 调整图像大小
        resized_img = image.resize((target_width, target_height))
        processed_images = []

        for i in range(blocks):
            box = (
                (i % (target_width // image_size)) * image_size,
                (i // (target_width // image_size)) * image_size,
                ((i % (target_width // image_size)) + 1) * image_size,
                ((i // (target_width // image_size)) + 1) * image_size
            )
            # 分割图像
            split_img = resized_img.crop(box)
            processed_images.append(split_img)

        assert len(processed_images) == blocks

        if use_thumbnail and len(processed_images) != 1:
            thumbnail_img = image.resize((image_size, image_size))
            processed_images.append(thumbnail_img)

        return processed_images

    def convert(self, images: Union[Image.Image, List[Image.Image]],
                output_format: str = 'html', **kwargs) -> List[str]:
        """
        转换图片为指定格式

        Args:
            images: 输入图片或图片列表
            output_format: 输出格式 ('html', 'latex', 'markdown')

        Returns:
            转换结果列表
        """
        if output_format not in self.supported_output_format:
            raise ValueError(f"不支持的输出格式: {output_format}. 支持的格式: {self.supported_output_format}")

        # 处理图像输入
        if not isinstance(images, list):
            images = [images]

        pixel_values_list = []
        for image in images:
            # 动态预处理图像
            patch_images = self._dynamic_preprocess(
                image, image_size=448, max_num=12
            )
            pixel_values = self.image_processor(
                patch_images,
                return_tensors='pt'
            )['pixel_values'].to(torch.bfloat16)
            pixel_values_list.append(pixel_values)

        batch_size = len(pixel_values_list)
        conversation_list = []

        for bs_idx in range(batch_size):
            pixel_values = pixel_values_list[bs_idx].to(torch.bfloat16)

            image_tokens = self._format_image_tokens(pixel_values.shape[0])
            question = '<image>\n' + self.prompt_template[output_format]
            answer = None

            # 获取模板名称，如果模型配置不可用则使用默认模板
            template_name = getattr(self.model.config, 'template', 'Hermes-2') if hasattr(self.model, 'config') and self.model.config else 'Hermes-2'
            template = get_conv_template(template_name)
            template.append_message(template.roles[0], question)
            template.append_message(template.roles[1], answer)
            conversation = template.get_prompt()
            conversation = conversation.replace('<image>', image_tokens, 1)
            conversation_list.append(conversation)

        device = next(self.parameters()).device
        self.tokenizer.padding_side = 'left'
        model_inputs = self.tokenizer(
            conversation_list,
            return_tensors='pt',
            padding=True,
            max_length=self.tokenizer.model_max_length,
            truncation=True,
        ).to(device)
        pixel_values = torch.cat(pixel_values_list, axis=0).to(device)

        # 生成配置
        generation_config = dict(
            max_new_tokens=self.max_new_tokens,
            max_time=self.max_generate_time,
            img_context_token_id=self.img_context_token_id,
            pad_token_id=self.tokenizer.pad_token_id,
            eos_token_id=self.tokenizer.eos_token_id,
            do_sample=False,
            no_repeat_ngram_size=20,
        )

        # 从图像token生成文本
        with torch.no_grad():
            model_output = self.model.generate(
                pixel_values=pixel_values,
                input_ids=model_inputs.input_ids,
                attention_mask=model_inputs.attention_mask,
                **generation_config,
            )

        batch_decode_texts = self.tokenizer.batch_decode(
            model_output,
            skip_special_tokens=True
        )
        return batch_decode_texts

    def convert_to_html(self, image: Union[Image.Image, str]) -> str:
        """
        将图片转换为HTML格式

        Args:
            image: PIL图片对象或图片路径

        Returns:
            HTML格式的表格代码
        """
        if isinstance(image, str):
            image = Image.open(image)

        results = self.convert(image, output_format='html')
        return results[0] if results else ""

    def convert_to_markdown(self, image: Union[Image.Image, str]) -> str:
        """
        将图片转换为Markdown格式

        Args:
            image: PIL图片对象或图片路径

        Returns:
            Markdown格式的表格代码
        """
        if isinstance(image, str):
            image = Image.open(image)

        results = self.convert(image, output_format='markdown')
        return results[0] if results else ""

    def convert_to_latex(self, image: Union[Image.Image, str]) -> str:
        """
        将图片转换为LaTeX格式

        Args:
            image: PIL图片对象或图片路径

        Returns:
            LaTeX格式的表格代码
        """
        if isinstance(image, str):
            image = Image.open(image)

        results = self.convert(image, output_format='latex')
        return results[0] if results else ""


def create_converter(model_path: str, **kwargs) -> ImageToHTMLConverter:
    """
    创建图片转HTML转换器的便捷函数

    Args:
        model_path: 模型路径
        **kwargs: 其他参数

    Returns:
        ImageToHTMLConverter实例
    """
    print('model_path', model_path)
    return ImageToHTMLConverter(model_path=model_path, **kwargs)


# 使用示例
if __name__ == "__main__":
    # 创建转换器
    converter = create_converter()

    # 转换图片
    image_path = "demo.png"  # 替换为实际图片路径

    try:
        # 转换为HTML
        html_result = converter.convert_to_html(image_path)
        print("HTML结果:")
        print(html_result)

        # 转换为Markdown
        markdown_result = converter.convert_to_markdown(image_path)
        print("\nMarkdown结果:")
        print(markdown_result)

    except Exception as e:
        print(f"转换失败: {e}")
