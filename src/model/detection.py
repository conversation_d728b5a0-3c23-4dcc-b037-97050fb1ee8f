import os
import logging
from typing import List, Dict
import numpy as np
from PIL import Image, ImageDraw, ImageFont

from src.config.settings import TABLE_DETECTION, OUTPUT

logger = logging.getLogger(__name__)


class TableDetector:
    """简化的表格检测器 - 只使用模型检测"""

    def __init__(self, model_manager):
        """
        初始化表格检测器
        
        Args:
            model_manager: 模型管理器
        """
        self.model_manager = model_manager
        self.config = TABLE_DETECTION
        logger.info("TableDetector initialized.")

    def detect_tables(self, image: Image.Image, page_idx: int = 0, output_dir: str = None) -> List[Dict]:
        """
        检测图像中的表格
        
        Args:
            image: PIL图像对象
            page_idx: 页面索引
            output_dir: 输出目录

        Returns:
            表格检测结果列表
        """
        if not self.model_manager.has_layout_model():
            logger.warning("DocLayout-YOLO model not loaded, cannot detect tables.")
            return []

        logger.info(f"Starting table detection for page {page_idx + 1}.")
        all_detections, high_conf_tables = self._detect_with_model(image)

        if self.config["remove_overlapping"]:
            high_conf_tables = self._remove_overlapping_tables(high_conf_tables)

        if self.config["verbose_stats"]:
            self._print_detection_stats(all_detections, high_conf_tables, page_idx)
        
        if self.config["save_layout_detection"] and output_dir and all_detections:
            self._save_layout_detection_result(image, all_detections, page_idx, output_dir)
        
        logger.info(f"Finished table detection for page {page_idx + 1}. Found {len(high_conf_tables)} high confidence tables.")
        return high_conf_tables

    def _detect_with_model(self, image: Image.Image) -> tuple:
        """使用DocLayout-YOLO模型检测"""
        layout_model = self.model_manager.get_layout_model()
        all_detections = []
        all_tables = []
        high_conf_tables = []

        if self.model_manager.is_mineru_available():
            logger.debug("Using MinerU's DocLayout-YOLO model.")
            predictions = layout_model.predict(image)
            
            for pred in predictions:
                category_id = pred.get("category_id", -1)
                score = pred.get("score", 0.0)
                
                poly = pred.get("poly", [])
                if len(poly) >= 8:
                    x_coords = [poly[i] for i in range(0, len(poly), 2)]
                    y_coords = [poly[i] for i in range(1, len(poly), 2)]
                    x1, y1 = min(x_coords), min(y_coords)
                    x2, y2 = max(x_coords), max(y_coords)
                    
                    if x2 > x1 and y2 > y1:
                        all_detections.append({
                            "bbox": [int(x1), int(y1), int(x2), int(y2)],
                            "category_id": category_id,
                            "score": score,
                            "category_name": self._get_category_name(category_id)
                        })
                        
                        if self.model_manager.is_table_category(category_id):
                            table_info = {
                                "bbox": [int(x1), int(y1), int(x2), int(y2)],
                                "category": "table",
                                "score": score,
                            }
                            all_tables.append(table_info)
                            
                            if score > self.config["conf_threshold"]:
                                high_conf_tables.append(table_info)
        else:
            logger.debug("Using original DocLayout YOLO model.")
            img_array = np.array(image)
            predictions = layout_model.predict(
                img_array,
                imgsz=self.config["imgsz"],
                conf=0.1,  # Low threshold to get all detections
                iou=self.config["iou_threshold"],
                verbose=False
            )[0]
            
            if hasattr(predictions, "boxes") and predictions.boxes is not None:
                for xyxy, conf, cls in zip(
                        predictions.boxes.xyxy.cpu(),
                        predictions.boxes.conf.cpu(),
                        predictions.boxes.cls.cpu(),
                ):
                    category_id = int(cls.item())
                    score = float(conf.item())
                    coords = list(map(int, xyxy.tolist()))
                    x1, y1, x2, y2 = coords
                    
                    if x2 > x1 and y2 > y1:
                        all_detections.append({
                            "bbox": [x1, y1, x2, y2],
                            "category_id": category_id,
                            "score": score,
                            "category_name": self._get_category_name(category_id)
                        })
                        
                        if self.model_manager.is_table_category(category_id):
                            table_info = {
                                "bbox": [x1, y1, x2, y2],
                                "category": "table",
                                "score": score,
                            }
                            all_tables.append(table_info)
                            
                            if score > self.config["conf_threshold"]:
                                high_conf_tables.append(table_info)

        return all_detections, high_conf_tables

    def _get_category_name(self, category_id: int) -> str:
        """获取类别名称"""
        category_map = {
            0: "title",
            1: "plain_text", 
            2: "abandon",
            3: "figure",
            4: "figure_caption",
            5: "table",
            6: "table_caption",
            7: "table_footnote",
            8: "isolate_formula",
            9: "formula_caption"
        }
        return category_map.get(category_id, f"unknown_{category_id}")

    def _remove_overlapping_tables(self, tables: List[Dict]) -> List[Dict]:
        """
        去除重叠的表格，只保留最大的表格

        Args:
            tables: 表格检测结果列表

        Returns:
            去重后的表格列表
        """
        if len(tables) <= 1:
            return tables

        logger.info(f"Removing overlapping tables. Initial count: {len(tables)}")
        sorted_tables = sorted(tables, key=lambda t: self._calculate_area(t["bbox"]), reverse=True)

        filtered_tables = []

        for table in sorted_tables:
            bbox1 = table["bbox"]
            is_overlapping = False

            for kept_table in filtered_tables:
                bbox2 = kept_table["bbox"]

                overlap_ratio = self._calculate_overlap_ratio(bbox1, bbox2)

                if overlap_ratio > self.config["overlap_threshold"]:
                    is_overlapping = True
                    area1 = self._calculate_area(bbox1)
                    area2 = self._calculate_area(bbox2)
                    logger.debug(f"Found overlapping tables: bbox1={bbox1}, area1={area1}; bbox2={bbox2}, area2={area2}; overlap_ratio={overlap_ratio:.3f}. Keeping larger table.")
                    break

            if not is_overlapping:
                filtered_tables.append(table)

        if len(filtered_tables) < len(tables):
            removed_count = len(tables) - len(filtered_tables)
            logger.info(f"Removed {removed_count} overlapping tables, {len(filtered_tables)} tables remaining.")

        return filtered_tables

    def _calculate_area(self, bbox: List[int]) -> int:
        """计算边界框面积"""
        x1, y1, x2, y2 = bbox
        return (x2 - x1) * (y2 - y1)

    def _calculate_overlap_ratio(self, bbox1: List[int], bbox2: List[int]) -> float:
        """
        计算两个边界框的重叠比例

        Args:
            bbox1: 第一个边界框 [x1, y1, x2, y2]
            bbox2: 第二个边界框 [x1, y1, x2, y2]

        Returns:
            重叠比例 (相对于较小表格的比例)
        """
        x1_1, y1_1, x2_1, y2_1 = bbox1
        x1_2, y1_2, x2_2, y2_2 = bbox2

        overlap_x1 = max(x1_1, x1_2)
        overlap_y1 = max(y1_1, y1_2)
        overlap_x2 = min(x2_1, x2_2)
        overlap_y2 = min(y2_1, y2_2)

        if overlap_x1 >= overlap_x2 or overlap_y1 >= overlap_y2:
            return 0.0

        overlap_area = (overlap_x2 - overlap_x1) * (overlap_y2 - overlap_y1)

        area1 = (x2_1 - x1_1) * (y2_1 - y1_1)
        area2 = (x2_2 - x1_2) * (y2_2 - y1_2)

        min_area = min(area1, area2)
        return overlap_area / min_area if min_area > 0 else 0.0

    def _print_detection_stats(self, all_detections: List[Dict], high_conf_tables: List[Dict], page_idx: int):
        """打印检测统计信息"""
        all_tables = [d for d in all_detections if d["category_name"] == "table"]
        
        logger.info(f"\nTable Detection Detailed Statistics (Page {page_idx + 1}):")
        logger.info(f"  Total tables detected: {len(all_tables)}")
        
        if all_tables:
            conf_ranges = [
                (self.config["conf_threshold"], 1.0, f"Very High Confidence (≥{self.config['conf_threshold']})"),
                (0.7, self.config["conf_threshold"], f"High Confidence (0.7-{self.config['conf_threshold']})"),
                (0.5, 0.7, "Medium Confidence (0.5-0.7)"),
                (0.3, 0.5, "Low Confidence (0.3-0.5)"),
                (0.0, 0.3, "Very Low Confidence (<0.3)")
            ]
            
            for min_conf, max_conf, label in conf_ranges:
                count = len([t for t in all_tables if min_conf <= t["score"] < max_conf])
                if count > 0:
                    logger.info(f"    {label}: {count} items")
                    for table in all_tables:
                        if min_conf <= table["score"] < max_conf:
                            bbox = table["bbox"]
                            score = table["score"]
                            logger.debug(f"      - bbox=({bbox[0]}, {bbox[1]}, {bbox[2]}, {bbox[3]}), score={score:.3f}")
            
            logger.info(f"  Final retained high confidence tables: {len(high_conf_tables)} (confidence>{self.config['conf_threshold']})")
            
            if len(all_tables) > len(high_conf_tables):
                filtered_count = len(all_tables) - len(high_conf_tables)
                logger.info(f"  Filtered out tables: {filtered_count} (confidence≤{self.config['conf_threshold']})")
        else:
            logger.info(f"  No tables detected.")

    def _save_layout_detection_result(self, image: Image.Image, detections: List[Dict], 
                                    page_idx: int, output_dir: str):
        """保存布局检测结果图片"""
        layout_dir = os.path.join(output_dir, "layout_detection")
        os.makedirs(layout_dir, exist_ok=True)
        
        result_image = image.copy()
        draw = ImageDraw.Draw(result_image)
        
        color_map = {
            "title": "#FF0000",        # Red
            "plain_text": "#00FF00",   # Green
            "abandon": "#808080",      # Gray
            "figure": "#0000FF",       # Blue
            "figure_caption": "#00FFFF", # Cyan
            "table": "#FF00FF",        # Magenta
            "table_caption": "#FFFF00", # Yellow
            "table_footnote": "#FFA500", # Orange
            "isolate_formula": "#800080", # Purple
            "formula_caption": "#FFC0CB"  # Pink
        }
        
        for detection in detections:
            bbox = detection["bbox"]
            category_name = detection["category_name"]
            score = detection["score"]
            
            x1, y1, x2, y2 = bbox
            color = color_map.get(category_name, "#000000")
            
            draw.rectangle([x1, y1, x2, y2], outline=color, width=3)
            
            label = f"{category_name}: {score:.2f}"
            
            try:
                font = ImageFont.truetype("/usr/share/fonts/truetype/dejavu/DejaVuSans-Bold.ttf", 16)
            except IOError:
                logger.warning("Default font not found, using ImageFont.load_default().")
                font = ImageFont.load_default()
            
            bbox_text = draw.textbbox((0, 0), label, font=font)
            text_width = bbox_text[2] - bbox_text[0]
            text_height = bbox_text[3] - bbox_text[1]
            
            draw.rectangle([x1, y1-text_height-5, x1+text_width+10, y1], fill=color)
            
            draw.text((x1+5, y1-text_height-2), label, fill="white", font=font)
        
        output_filename = f"page_{page_idx}_layout_detection.jpg"
        output_path = os.path.join(layout_dir, output_filename)
        result_image.save(output_path, OUTPUT["image_format"], quality=OUTPUT["image_quality"])
        
        logger.info(f"Layout detection result saved: {output_path}")
        logger.info(f"Detected {len(detections)} layout elements.")
        
        category_counts = {}
        for detection in detections:
            category = detection["category_name"]
            category_counts[category] = category_counts.get(category, 0) + 1
        
        logger.info("Layout element statistics:")
        for category, count in sorted(category_counts.items()):
            color = color_map.get(category, "#000000")
            logger.info(f"  {category}: {count} items (Color: {color})")
