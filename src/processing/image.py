import os
import logging
from typing import List, Dict, Tuple

from PIL import Image, ImageEnhance

from src.utils.helpers import validate_bbox
from src.config.settings import OUTPUT

logger = logging.getLogger(__name__)


class ImageProcessor:
    """图像处理器"""

    def __init__(self):
        """
        初始化图像处理器
        """
        self.max_width = OUTPUT["max_image_width"]
        self.max_height = OUTPUT["max_image_height"]
        logger.info(f"ImageProcessor initialized with max_width: {self.max_width}, max_height: {self.max_height}")

    def crop_table_images(self, image: Image.Image, table_bboxes: List[Dict],
                          page_idx: int, scale: float) -> List[Dict]:
        """
        从图像中裁剪表格区域
        
        Args:
            image: PIL图像对象
            table_bboxes: 表格边界框列表
            page_idx: 页面索引
            scale: 缩放比例
            
        Returns:
            裁剪后的表格图像信息列表
        """
        table_images = []
        image_size = image.size
        logger.info(f"Cropping table images from page {page_idx} with {len(table_bboxes)} bboxes.")

        for table_idx, table_info in enumerate(table_bboxes):
            bbox = table_info["bbox"]

            # 验证边界框
            if not validate_bbox(bbox, image_size):
                logger.warning(f"Invalid bbox {bbox} for image size {image_size}. Skipping table {table_idx} on page {page_idx}.")
                continue

            x1, y1, x2, y2 = bbox

            # 裁剪图像
            cropped_image = image.crop((x1, y1, x2, y2))

            # 限制图像尺寸
            cropped_image = self.resize_image(cropped_image, max_size=(self.max_width, self.max_height))

            # 生成文件名
            filename = f"page_{page_idx}_table_{table_idx}_{x1}_{y1}_{x2}_{y2}.jpg"

            table_images.append({
                "image": cropped_image,
                "filename": filename,
                "bbox": bbox,
                "page_idx": page_idx,
                "table_idx": table_idx,
                "score": table_info.get("score", 0.0)
            })
        logger.info(f"Finished cropping {len(table_images)} table images from page {page_idx}.")
        return table_images

    def save_table_images(self, table_images: List[Dict], output_dir: str) -> List[str]:
        """
        保存表格图像到文件
        
        Args:
            table_images: 表格图像信息列表
            output_dir: 输出目录
            
        Returns:
            保存的文件路径列表
        """
        os.makedirs(output_dir, exist_ok=True)
        saved_files = []
        logger.info(f"Saving {len(table_images)} table images to {output_dir}.")

        for table_info in table_images:
            image = table_info["image"]
            filename = table_info["filename"]
            filepath = os.path.join(output_dir, filename)

            # 保存图像
            image.save(filepath, OUTPUT["image_format"], quality=OUTPUT["image_quality"])
            saved_files.append(filepath)
            logger.debug(f"Saved {filename}")

        logger.info(f"Finished saving {len(saved_files)} table images.")
        return saved_files

    def resize_image(self, image: Image.Image, max_size: Tuple[int, int]) -> Image.Image:
        """
        调整图像大小
        
        Args:
            image: PIL图像对象
            max_size: 最大尺寸 (width, height)
            
        Returns:
            调整后的图像
        """
        width, height = image.size
        max_width, max_height = max_size

        # 计算缩放比例
        scale_w = max_width / width
        scale_h = max_height / height
        scale = min(scale_w, scale_h, 1.0)  # 不放大

        if scale < 1.0:
            new_width = int(width * scale)
            new_height = int(height * scale)
            return image.resize((new_width, new_height), Image.Resampling.LANCZOS)

        return image

    def enhance_image(self, image: Image.Image) -> Image.Image:
        """
        增强图像质量
        
        Args:
            image: PIL图像对象
            
        Returns:
            增强后的图像
        """
        # 增强对比度
        enhancer = ImageEnhance.Contrast(image)
        image = enhancer.enhance(1.2)

        # 增强锐度
        enhancer = ImageEnhance.Sharpness(image)
        image = enhancer.enhance(1.1)

        return image

    def convert_to_grayscale(self, image: Image.Image) -> Image.Image:
        """
        转换为灰度图像
        
        Args:
            image: PIL图像对象
            
        Returns:
            灰度图像
        """
        return image.convert('L')

    def get_image_info(self, image: Image.Image) -> Dict:
        """
        获取图像信息
        
        Args:
            image: PIL图像对象
            
        Returns:
            图像信息字典
        """
        return {
            "size": image.size,
            "mode": image.mode,
            "format": image.format,
            "width": image.width,
            "height": image.height
        }
