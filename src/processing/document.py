import os
import subprocess
import tempfile
import shutil
import logging
from typing import List, Dict

from src.processing.pdf import PDFProcessor
from src.config.settings import DEFAULT_DPI

logger = logging.getLogger(__name__)


class DocumentProcessor:
    """文档处理器 - 支持DOCX和DOC文件"""

    def __init__(self, dpi: int = DEFAULT_DPI):
        """
        初始化文档处理器
        
        Args:
            dpi: 图像分辨率
        """
        self.dpi = dpi
        self.temp_dir = tempfile.mkdtemp()
        logger.info(f"DocumentProcessor initialized with DPI: {self.dpi}, temp_dir: {self.temp_dir}")

    def document_to_images(self, doc_path: str) -> List[Dict]:
        """
        将文档转换为图像列表
        
        Args:
            doc_path: 文档文件路径
            
        Returns:
            包含页面信息的字典列表
        """
        file_ext = os.path.splitext(doc_path)[1].lower()

        if file_ext == '.docx':
            logger.info(f"Processing DOCX file: {doc_path}")
            return self._process_docx(doc_path)
        elif file_ext == '.doc':
            logger.info(f"Processing DOC file: {doc_path}")
            return self._process_doc(doc_path)
        else:
            logger.error(f"Unsupported file format: {file_ext} for {doc_path}")
            raise ValueError(f"不支持的文件格式: {file_ext}")

    def _process_docx(self, docx_path: str) -> List[Dict]:
        """
        处理DOCX文件
        """
        # 优先使用LibreOffice转换为PDF，获得更好的布局保持
        logger.info(f"Converting DOCX to PDF for processing: {docx_path}")
        return self._convert_to_pdf_and_process(docx_path)

    def _process_doc(self, doc_path: str) -> List[Dict]:
        """
        处理DOC文件
        """
        # DOC文件需要先转换为DOCX或PDF
        logger.info(f"Converting DOC to PDF for processing: {doc_path}")
        return self._convert_to_pdf_and_process(doc_path)

    def _convert_to_pdf_and_process(self, doc_path: str) -> List[Dict]:
        """
        将文档转换为PDF再处理
        """
        pdf_path = self._convert_to_pdf(doc_path)
        pdf_processor = PDFProcessor(dpi=self.dpi)
        return pdf_processor.pdf_to_images(pdf_path)

    def _convert_to_pdf(self, doc_path: str) -> str:
        """
        使用LibreOffice将文档转换为PDF
        """
        output_dir = self.temp_dir
        logger.info(f"Converting {doc_path} to PDF using LibreOffice. Output directory: {output_dir}")

        # 构建LibreOffice命令
        cmd = [
            'libreoffice',
            '--headless',
            '--convert-to', 'pdf',
            '--outdir', output_dir,
            doc_path
        ]

        # 执行转换
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)

        if result.returncode != 0:
            logger.error(f"LibreOffice conversion failed for {doc_path}: {result.stderr}")
            raise RuntimeError(f"LibreOffice转换失败: {result.stderr}")

        # 返回生成的PDF路径
        base_name = os.path.splitext(os.path.basename(doc_path))[0]
        pdf_path = os.path.join(output_dir, f"{base_name}.pdf")

        if not os.path.exists(pdf_path):
            logger.error(f"Converted PDF file not found: {pdf_path}")
            raise FileNotFoundError(f"转换后的PDF文件不存在: {pdf_path}")
        
        logger.info(f"Successfully converted {doc_path} to {pdf_path}")
        return pdf_path

    def get_page_count(self, doc_path: str) -> int:
        """
        获取文档页面数量
        
        Args:
            doc_path: 文档文件路径
            
        Returns:
            页面数量
        """
        pages = self.document_to_images(doc_path)
        return len(pages)

    def extract_single_page(self, doc_path: str, page_idx: int) -> Dict:
        """
        提取单个页面
        
        Args:
            doc_path: 文档文件路径
            page_idx: 页面索引
            
        Returns:
            页面信息字典
        """
        pages = self.document_to_images(doc_path)

        if page_idx >= len(pages):
            raise IndexError(f"页面索引 {page_idx} 超出范围，总页数: {len(pages)}")

        return pages[page_idx]

    def __del__(self):
        """
        清理临时文件
        """
        try:
            if hasattr(self, 'temp_dir') and os.path.exists(self.temp_dir):
                shutil.rmtree(self.temp_dir)
                logger.info(f"Cleaned up temporary directory: {self.temp_dir}")
        except Exception as e:
            logger.error(f"Error cleaning up temporary directory {self.temp_dir}: {e}")
