import logging
from typing import List, Dict

import pypdfium2 as pdfium
from tqdm import tqdm

from src.config.settings import DEFAULT_DPI

logger = logging.getLogger(__name__)


class PDFProcessor:
    """PDF处理器"""

    def __init__(self, dpi: int = DEFAULT_DPI):
        """
        初始化PDF处理器
        
        Args:
            dpi: 图像分辨率
        """
        self.dpi = dpi
        logger.info(f"PDFProcessor initialized with DPI: {self.dpi}")

    def pdf_to_images(self, pdf_path: str) -> List[Dict]:
        """
        将PDF转换为图像列表

        Args:
            pdf_path: PDF文件路径

        Returns:
            包含页面信息的字典列表，每个字典包含：
            - page_idx: 页面索引
            - image: PIL图像对象
            - scale: 缩放比例
        """
        logger.info(f"Converting PDF to images: {pdf_path}")
        pdf = pdfium.PdfDocument(pdf_path)
        pages = []

        for page_idx in tqdm(range(len(pdf)), desc="Converting PDF pages", unit="page"):
            page = pdf[page_idx]

            # 计算缩放比例
            scale = self.dpi / 72.0

            # 渲染页面
            bitmap = page.render(scale=scale)
            pil_image = bitmap.to_pil()

            pages.append({
                "page_idx": page_idx,
                "image": pil_image,
                "scale": scale
            })

        pdf.close()
        logger.info(f"Successfully converted {len(pages)} pages from {pdf_path} to images.")
        return pages

    def get_page_count(self, pdf_path: str) -> int:
        """
        获取PDF页面数量
        
        Args:
            pdf_path: PDF文件路径
            
        Returns:
            页面数量
        """
        logger.info(f"Getting page count for PDF: {pdf_path}")
        pdf = pdfium.PdfDocument(pdf_path)
        count = len(pdf)
        pdf.close()
        logger.info(f"PDF {pdf_path} has {count} pages.")
        return count

    def extract_single_page(self, pdf_path: str, page_idx: int) -> Dict:
        """
        提取单个页面
        
        Args:
            pdf_path: PDF文件路径
            page_idx: 页面索引
            
        Returns:
            页面信息字典
        """
        logger.info(f"Extracting single page {page_idx} from PDF: {pdf_path}")
        pdf = pdfium.PdfDocument(pdf_path)

        if page_idx >= len(pdf):
            pdf.close()
            raise IndexError(f"页面索引 {page_idx} 超出范围，总页数: {len(pdf)}")

        page = pdf[page_idx]
        scale = self.dpi / 72.0
        bitmap = page.render(scale=scale)
        pil_image = bitmap.to_pil()

        result = {
            "page_idx": page_idx,
            "image": pil_image,
            "scale": scale
        }

        pdf.close()
        logger.info(f"Successfully extracted page {page_idx} from {pdf_path}.")
        return result
