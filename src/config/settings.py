import os
from dotenv import load_dotenv

load_dotenv()

def get_env_str(key: str, default: str) -> str:
    return os.getenv(key, default)

def get_env_int(key: str, default: int) -> int:
    return int(os.getenv(key, default))

def get_env_float(key: str, default: float) -> float:
    return float(os.getenv(key, default))

def get_env_bool(key: str, default: bool) -> bool:
    return os.getenv(key, str(default)).lower() in ('true', '1', 't')

def get_env_list(key: str, default: str) -> list:
    return [item.strip() for item in os.getenv(key, default).split(',')]

# General
DEFAULT_DPI = get_env_int("DEFAULT_DPI", 200)
DEFAULT_DEVICE = get_env_str("DEFAULT_DEVICE", "cuda")
DEFAULT_CONFIDENCE_THRESHOLD = get_env_float("DEFAULT_CONFIDENCE_THRESHOLD", 0.05)
DEFAULT_IOU_THRESHOLD = get_env_float("DEFAULT_IOU_THRESHOLD", 0.45)
DEFAULT_IMAGE_SIZE = get_env_int("DEFAULT_IMAGE_SIZE", 1280)

# Table Detection
TABLE_DETECTION = {
    "conf_threshold": get_env_float("TABLE_DETECTION_CONF_THRESHOLD", 0.1),
    "iou_threshold": get_env_float("TABLE_DETECTION_IOU_THRESHOLD", 0.6),
    "imgsz": get_env_int("TABLE_DETECTION_IMGSZ", 1280),
    "save_layout_detection": get_env_bool("TABLE_DETECTION_SAVE_LAYOUT_DETECTION", True),
    "verbose_stats": get_env_bool("TABLE_DETECTION_VERBOSE_STATS", True),
    "overlap_threshold": get_env_float("TABLE_DETECTION_OVERLAP_THRESHOLD", 0.2),
    "remove_overlapping": get_env_bool("TABLE_DETECTION_REMOVE_OVERLAPPING", True),
    "min_area": get_env_int("TABLE_DETECTION_MIN_AREA", 100),
    "min_width": get_env_int("TABLE_DETECTION_MIN_WIDTH", 50),
    "min_height": get_env_int("TABLE_DETECTION_MIN_HEIGHT", 30),
    "max_aspect_ratio": get_env_int("TABLE_DETECTION_MAX_ASPECT_RATIO", 10),
    "min_aspect_ratio": get_env_float("TABLE_DETECTION_MIN_ASPECT_RATIO", 0.1),
}

# Post Processing
POST_PROCESSING = {
    "iou_threshold": get_env_float("POST_PROCESSING_IOU_THRESHOLD", 0.7),
    "overlap_threshold": get_env_float("POST_PROCESSING_OVERLAP_THRESHOLD", 0.8),
    "area_threshold": get_env_float("POST_PROCESSING_AREA_THRESHOLD", 0.8),
}

# Model Paths
MODEL_PATHS = {
    "doclayout_yolo": get_env_str("MODEL_PATH_DOCLAYOUT_YOLO", "models/DocLayout-YOLO-DocStructBench/doclayout_yolo_docstructbench_imgsz1024.pt"),
    "slanet_plus": get_env_str("MODEL_PATH_SLANET_PLUS", "models/SLANet_plus"),
    "image_to_html": get_env_str("MODEL_PATH_IMAGE_TO_HTML", "models/StructTable-InternVL2-1B"),
}

# Output
OUTPUT = {
    "image_format": get_env_str("OUTPUT_IMAGE_FORMAT", "JPEG"),
    "image_quality": get_env_int("OUTPUT_IMAGE_QUALITY", 95),
    "info_filename": get_env_str("OUTPUT_INFO_FILENAME", "extraction_info.json"),
    "max_image_width": get_env_int("OUTPUT_MAX_IMAGE_WIDTH", 3000),
    "max_image_height": get_env_int("OUTPUT_MAX_IMAGE_HEIGHT", 3000),
}

# Image to HTML
IMAGE_TO_HTML = {
    "enabled": get_env_bool("IMAGE_TO_HTML_ENABLED", True),
    "model_path": get_env_str("MODEL_PATH_IMAGE_TO_HTML", "models/StructTable-InternVL2-1B"),
    "output_formats": get_env_list("IMAGE_TO_HTML_OUTPUT_FORMATS", "html,markdown"),
    "max_new_tokens": get_env_int("IMAGE_TO_HTML_MAX_NEW_TOKENS", 1024),
    "max_time": get_env_int("IMAGE_TO_HTML_MAX_TIME", 60),
    "flash_attn": get_env_bool("IMAGE_TO_HTML_FLASH_ATTN", True),
    "device": get_env_str("IMAGE_TO_HTML_DEVICE", "cuda"),
    "save_html_files": get_env_bool("IMAGE_TO_HTML_SAVE_FILES", True),
    "html_output_dir": get_env_str("IMAGE_TO_HTML_OUTPUT_DIR", "html_output"),
}

# Logging
LOGGING = {
    "level": get_env_str("LOGGING_LEVEL", "INFO"),
    "format": get_env_str("LOGGING_FORMAT", "<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>"),
}

# llm 配置如下 请修改为同上面的配置代码一样

"""
配置模块
"""
import os
from dataclasses import dataclass
from dotenv import load_dotenv

# 加载环境变量
load_dotenv(os.path.join(os.path.dirname(__file__), '.env'))


@dataclass
class ModelConfig:
    """模型配置"""
    # LLM配置
    llm_model_name: str = os.getenv('LLM_MODEL_NAME', 'qwen-turbo')
    llm_base_url: str = os.getenv('LLM_BASE_URL', 'http://192.168.50.31:3010/v1')
    llm_api_key: str = os.getenv('LLM_API_KEY', '')
    llm_max_tokens: int = int(os.getenv('LLM_MAX_TOKENS', '16384'))
    llm_temperature: float = float(os.getenv('LLM_TEMPERATURE', '0.1'))
    llm_timeout: int = int(os.getenv('LLM_TIMEOUT', '60'))

    # Embedding配置
    embedding_model_name: str = os.getenv('EMBEDDING_MODEL_NAME', 'nomic-embed-text:v1.5')
    embedding_api_key: str = os.getenv('EMBEDDING_API_KEY', '')
    embedding_base_url: str = os.getenv('EMBEDDING_BASE_URL', 'http://192.168.50.31:3010/v1')
    embedding_dimension: int = int(os.getenv('EMBEDDING_DIMENSION', '768'))
    embedding_batch_size: int = int(os.getenv('EMBEDDING_BATCH_SIZE', '10'))


@dataclass
class Config:
    """全局配置"""
    model: ModelConfig = None

    def __post_init__(self):
        if self.model is None:
            self.model = ModelConfig()


# 全局配置实例
config = Config()
