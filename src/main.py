#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PDF表格提取主程序
组装所有功能模块，提供完整的表格提取功能
"""

import argparse
import json
import os
import sys
import time
from typing import Dict

from tqdm import tqdm

from .config import OUTPUT, IMAGE_TO_HTML
from .document_processor import DocumentProcessor
from .html_converter import HTMLConverterManager
from .image_processor import ImageProcessor
from .model_manager import ModelManager
from .pdf_processor import PDFProcessor
from .table_detector import TableDetector


class DocumentTableExtractor:
    """文档表格提取器 - 支持PDF、DOCX、DOC文件"""

    def __init__(self, device: str = "cuda", dpi: int = 200, enable_html_conversion: bool = None):
        """
        初始化文档表格提取器

        Args:
            device: 设备类型 ("cpu" 或 "cuda")，默认使用CUDA
            dpi: 图像分辨率
            enable_html_conversion: 是否启用HTML转换功能，None表示使用配置文件设置
        """
        self.device = device
        self.dpi = dpi

        # 初始化各个模块
        self.pdf_processor = PDFProcessor(dpi=dpi)
        self.document_processor = DocumentProcessor(dpi=dpi)
        self.model_manager = ModelManager(device=device)
        self.table_detector = TableDetector(self.model_manager)
        self.image_processor = ImageProcessor(
            max_width=OUTPUT["max_image_width"],
            max_height=OUTPUT["max_image_height"]
        )

        # 初始化HTML转换器
        self.html_converter = None
        if enable_html_conversion is None:
            enable_html_conversion = IMAGE_TO_HTML.get("enabled", True)

        if enable_html_conversion:
            try:
                self.html_converter = HTMLConverterManager()
                print("✅ HTML转换功能已启用")
            except Exception as e:
                print(f"⚠️  HTML转换功能初始化失败: {e}")
                self.html_converter = None

        # 初始化模型
        self._init_models()

    def _init_models(self):
        """初始化模型"""
        self.model_manager.init_layout_model()
        self.model_manager.init_table_model()

    def _get_file_processor(self, file_path: str):
        """根据文件类型获取对应的处理器"""
        file_ext = os.path.splitext(file_path)[1].lower()

        if file_ext == '.pdf':
            return self.pdf_processor
        elif file_ext in ['.docx', '.doc']:
            return self.document_processor
        else:
            raise ValueError(f"不支持的文件格式: {file_ext}")

    def _file_to_images(self, file_path: str):
        """将文件转换为图像列表"""
        processor = self._get_file_processor(file_path)

        if hasattr(processor, 'pdf_to_images'):
            return processor.pdf_to_images(file_path)
        elif hasattr(processor, 'document_to_images'):
            return processor.document_to_images(file_path)
        else:
            raise RuntimeError("处理器不支持图像转换")

    def extract_tables_from_file(self, file_path: str, output_dir: str = None, convert_to_html: bool = True) -> Dict:
        """
        从文件中提取表格（支持PDF、DOCX、DOC）

        Args:
            file_path: 文件路径
            output_dir: 输出目录
            convert_to_html: 是否将提取的表格图像转换为HTML文件

        Returns:
            包含提取结果和HTML转换结果的字典
        """
        if output_dir is None:
            output_dir = f"extracted_tables_{int(time.time())}"

        os.makedirs(output_dir, exist_ok=True)

        # 获取文件类型
        file_ext = os.path.splitext(file_path)[1].lower()

        # 转换文件为图像
        pages = self._file_to_images(file_path)

        all_saved_files = []
        extraction_info = {
            "file_path": file_path,
            "file_type": file_ext,
            "output_dir": output_dir,
            "total_pages": len(pages),
            "tables_found": 0,
            "pages_with_tables": [],
            "extraction_time": 0
        }

        start_time = time.time()

        # 处理每一页，使用进度条
        desc = f"处理{file_ext.upper()}页面"
        for page_info in tqdm(pages, desc=desc, unit="页"):
            page_idx = page_info["page_idx"]
            image = page_info["image"]
            scale = page_info["scale"]

            # 检测表格
            table_bboxes = self.table_detector.detect_tables(image, page_idx, output_dir)

            if table_bboxes:
                # 裁剪表格图像
                table_images = self.image_processor.crop_table_images(
                    image, table_bboxes, page_idx, scale
                )

                # 保存表格图像
                saved_files = self.image_processor.save_table_images(
                    table_images, output_dir
                )

                all_saved_files.extend(saved_files)

                # 记录信息
                extraction_info["pages_with_tables"].append({
                    "page": page_idx + 1,
                    "table_count": len(table_images)
                })
                extraction_info["tables_found"] += len(table_images)

        extraction_info["extraction_time"] = round(time.time() - start_time, 2)

        # 保存提取信息
        info_file = os.path.join(output_dir, "extraction_info.json")
        with open(info_file, 'w', encoding='utf-8') as f:
            json.dump(extraction_info, f, ensure_ascii=False, indent=2)

        # 执行HTML转换
        html_conversion_result = None
        if convert_to_html and all_saved_files and self.html_converter and self.html_converter.is_enabled():
            print("\n开始执行图片转HTML...")
            try:
                html_output_dir = os.path.join(output_dir, "html_output")
                html_conversion_result = self.html_converter.convert_images_to_html(
                    all_saved_files, html_output_dir
                )
            except Exception as e:
                print(f"❌ HTML转换失败: {e}")
                html_conversion_result = {
                    "success": False,
                    "message": f"HTML转换失败: {e}",
                    "converted_files": []
                }

        return {
            "extracted_files": all_saved_files,
            "extraction_info": extraction_info,
            "html_conversion": html_conversion_result
        }

    def extract_single_page(self, file_path: str, page_idx: int, output_dir: str = None, convert_to_html: bool = True) -> Dict:
        """
        从文件的单个页面提取表格

        Args:
            file_path: 文件路径
            page_idx: 页面索引 (0-based)
            output_dir: 输出目录
            convert_to_html: 是否将提取的表格图像转换为HTML文件

        Returns:
            包含提取结果和HTML转换结果的字典
        """
        if output_dir is None:
            output_dir = f"extracted_tables_page_{page_idx}_{int(time.time())}"

        os.makedirs(output_dir, exist_ok=True)

        # 提取单个页面
        processor = self._get_file_processor(file_path)

        if hasattr(processor, 'extract_single_page'):
            page_info = processor.extract_single_page(file_path, page_idx)
        else:
            # 如果处理器不支持单页提取，则提取所有页面然后选择指定页面
            pages = self._file_to_images(file_path)
            if page_idx >= len(pages):
                raise IndexError(f"页面索引 {page_idx} 超出范围，总页数: {len(pages)}")
            page_info = pages[page_idx]

        image = page_info["image"]
        scale = page_info["scale"]

        # 检测表格
        table_bboxes = self.table_detector.detect_tables(image, page_idx, output_dir)

        if not table_bboxes:
            return {
                "extracted_files": [],
                "html_conversion": None
            }

        # 裁剪表格图像
        table_images = self.image_processor.crop_table_images(
            image, table_bboxes, page_idx, scale
        )

        # 保存表格图像
        saved_files = self.image_processor.save_table_images(
            table_images, output_dir
        )

        # 执行HTML转换
        html_conversion_result = None
        if convert_to_html and saved_files and self.html_converter and self.html_converter.is_enabled():
            print("\n开始执行图片转HTML...")
            try:
                html_output_dir = os.path.join(output_dir, "html_output")
                html_conversion_result = self.html_converter.convert_images_to_html(
                    saved_files, html_output_dir
                )
            except Exception as e:
                print(f"❌ HTML转换失败: {e}")
                html_conversion_result = {
                    "success": False,
                    "message": f"HTML转换失败: {e}",
                    "converted_files": []
                }

        return {
            "extracted_files": saved_files,
            "html_conversion": html_conversion_result
        }

    def get_extraction_stats(self, file_path: str) -> Dict:
        """
        获取文件的表格提取统计信息（不保存图像）

        Args:
            file_path: 文件路径

        Returns:
            统计信息字典
        """
        file_ext = os.path.splitext(file_path)[1].lower()
        pages = self._file_to_images(file_path)

        stats = {
            "file_type": file_ext,
            "total_pages": len(pages),
            "pages_with_tables": 0,
            "total_tables": 0,
            "page_details": []
        }

        desc = f"分析{file_ext.upper()}页面"
        for page_info in tqdm(pages, desc=desc, unit="页"):
            page_idx = page_info["page_idx"]
            image = page_info["image"]

            # 检测表格
            table_bboxes = self.table_detector.detect_tables(image, page_idx, output_dir)

            page_detail = {
                "page": page_idx + 1,
                "table_count": len(table_bboxes)
            }

            if table_bboxes:
                stats["pages_with_tables"] += 1
                stats["total_tables"] += len(table_bboxes)

            stats["page_details"].append(page_detail)

        return stats


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="文档表格提取工具 - 支持PDF、DOCX、DOC文件")
    parser.add_argument("file_path", help="文件路径 (PDF/DOCX/DOC)")
    parser.add_argument("output_dir", nargs="?", help="输出目录")
    parser.add_argument("--device", default="cuda", choices=["cpu", "cuda"], help="设备类型")
    parser.add_argument("--dpi", type=int, default=200, help="图像分辨率")
    parser.add_argument("--page", type=int, help="只处理指定页面 (0-based)")
    parser.add_argument("--stats-only", action="store_true", help="只显示统计信息，不保存图像")
    parser.add_argument("--max-size", type=int, default=3000, help="图像最大尺寸 (像素)")
    parser.add_argument("--no-html", action="store_true", help="禁用HTML转换功能")
    parser.add_argument("--html-formats", nargs="+", default=["html", "markdown"],
                        choices=["html", "markdown", "latex"], help="HTML转换输出格式")

    args = parser.parse_args()

    # 检查文件是否存在
    if not os.path.exists(args.file_path):
        print(f"错误: 文件不存在: {args.file_path}")
        sys.exit(1)

    # 检查文件格式
    file_ext = os.path.splitext(args.file_path)[1].lower()
    if file_ext not in ['.pdf', '.docx', '.doc']:
        print(f"错误: 不支持的文件格式: {file_ext}")
        print("支持的格式: PDF (.pdf), Word文档 (.docx, .doc)")
        sys.exit(1)

    # 初始化提取器
    extractor = DocumentTableExtractor(
        device=args.device,
        dpi=args.dpi,
        enable_html_conversion=not args.no_html
    )

    # 更新图像处理器的最大尺寸
    extractor.image_processor.max_width = args.max_size
    extractor.image_processor.max_height = args.max_size

    # 更新HTML转换器配置
    if extractor.html_converter and not args.no_html:
        extractor.html_converter.config["output_formats"] = args.html_formats

    if args.stats_only:
        # 只显示统计信息
        stats = extractor.get_extraction_stats(args.file_path)
        file_type = stats['file_type'].upper()
        print(f"{file_type}表格统计信息:")
        print(f"文件类型: {file_type}")
        print(f"总页数: {stats['total_pages']}")
        print(f"包含表格的页数: {stats['pages_with_tables']}")
        print(f"表格总数: {stats['total_tables']}")
        print("\n各页详情:")
        for detail in stats["page_details"]:
            if detail["table_count"] > 0:
                print(f"  第 {detail['page']} 页: {detail['table_count']} 个表格")

    elif args.page is not None:
        # 处理单个页面
        result = extractor.extract_single_page(
            args.file_path, args.page, args.output_dir, convert_to_html=not args.no_html
        )
        extracted_files = result["extracted_files"]
        html_conversion = result["html_conversion"]

        print(f"从第 {args.page + 1} 页提取了 {len(extracted_files)} 个表格")
        if extracted_files:
            print("保存的表格图像:")
            for file_path in extracted_files:
                print(f"  {file_path}")

        if html_conversion and html_conversion.get("success"):
            converted_files = html_conversion.get("converted_files", [])
            print(f"HTML转换完成！生成了 {len(converted_files)} 个文件")

    else:
        # 处理整个文件
        result = extractor.extract_tables_from_file(
            args.file_path, args.output_dir, convert_to_html=not args.no_html
        )
        extracted_files = result["extracted_files"]
        html_conversion = result["html_conversion"]

        print(f"提取完成！共保存 {len(extracted_files)} 个表格图像")
        if args.output_dir:
            print(f"保存位置: {args.output_dir}")

        if html_conversion and html_conversion.get("success"):
            converted_files = html_conversion.get("converted_files", [])
            print(f"HTML转换完成！生成了 {len(converted_files)} 个文件")


if __name__ == "__main__":
    main()
