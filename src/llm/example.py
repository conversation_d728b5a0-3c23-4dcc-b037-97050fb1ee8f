"""
LLM客户端使用示例
"""

import asyncio
from src.llm import call_llm, call_llm_stream, call_embedding


async def test_llm():
    """测试LLM调用"""
    print("🤖 测试LLM调用...")
    
    prompt = "请简单介绍一下Python编程语言的特点"
    response = await call_llm(prompt)
    print(f"LLM响应: {response[:100]}...")


async def test_llm_stream():
    """测试LLM流式调用"""
    print("\n🌊 测试LLM流式调用...")
    
    prompt = "请用一句话描述人工智能"
    print("流式响应: ", end="", flush=True)
    
    async for chunk in call_llm_stream(prompt):
        print(chunk, end="", flush=True)
    print()


async def test_embedding():
    """测试Embedding调用"""
    print("\n📊 测试Embedding调用...")
    
    texts = ["这是一个测试文本", "这是另一个测试文本"]
    embeddings = await call_embedding(texts)
    
    print(f"生成了 {len(embeddings)} 个向量")
    if embeddings:
        print(f"向量维度: {len(embeddings[0])}")
        print(f"第一个向量前5维: {embeddings[0][:5]}")


async def main():
    """主函数"""
    print("=" * 50)
    print("🚀 LLM客户端测试")
    print("=" * 50)
    
    try:
        await test_llm()
        await test_llm_stream()
        await test_embedding()
        
        print("\n✅ 所有测试完成!")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")


if __name__ == "__main__":
    asyncio.run(main())
