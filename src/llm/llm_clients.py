"""
大模型客户端模块

提供统一的LLM和Embedding调用接口，支持OpenAI兼容的API
"""

from typing import List, AsyncGenerator, Dict, Any

from openai import AsyncOpenAI

from .config import config
import logging

logger = logging.getLogger("llm_clients")

class LLMClient:
    """LLM客户端"""

    def __init__(self):
        self.model_config = config.model
        # 创建OpenAI客户端
        self.client = AsyncOpenAI(
            api_key=self.model_config.llm_api_key,
            base_url=self.model_config.llm_base_url,
            timeout=self.model_config.llm_timeout
        )

        logger.info(f"LLM客户端初始化完成: {self.model_config.llm_model_name} @ {self.model_config.llm_base_url}")

    async def call(self, prompt: str, **kwargs) -> str:
        """调用LLM"""
        # 合并参数，限制max_tokens不超过16384
        call_kwargs = {
            "model": self.model_config.llm_model_name,
            "messages": [{"role": "user", "content": prompt}],
            "temperature": self.model_config.llm_temperature,
            "max_tokens": self.model_config.llm_max_tokens,
            **kwargs
        }

        logger.debug(f"调用LLM: {self.model_config.llm_model_name}, prompt长度: {len(prompt)}")

        response = await self.client.chat.completions.create(**call_kwargs)

        if response.choices and response.choices[0].message:
            content = response.choices[0].message.content
            logger.debug(f"LLM响应长度: {len(content) if content else 0}")
            return content or ""
        else:
            logger.warning("LLM返回空响应")
            return ""

    async def call_stream(self, prompt: str, **kwargs) -> AsyncGenerator[str, None]:
        """流式调用LLM"""
        # 合并参数，强制启用流式
        call_kwargs = {
            "model": self.model_config.llm_model_name,
            "messages": [{"role": "user", "content": prompt}],
            "temperature": self.model_config.llm_temperature,
            "max_tokens": self.model_config.llm_max_tokens,
            "stream": True,
            **kwargs
        }

        logger.debug(f"流式调用LLM: {self.model_config.llm_model_name}, prompt长度: {len(prompt)}")

        try:
            stream = await self.client.chat.completions.create(**call_kwargs)

            async for chunk in stream:
                if chunk.choices and chunk.choices[0].delta:
                    content = chunk.choices[0].delta.content
                    if content:
                        yield content

        except Exception as e:
            logger.error(f"流式LLM调用失败: {e}")
            yield f"流式调用失败: {str(e)}"


class EmbeddingClient:
    """Embedding客户端"""

    def __init__(self):
        self.model_config = config.model

        # 创建OpenAI客户端
        self.client = AsyncOpenAI(
            api_key=self.model_config.embedding_api_key,
            base_url=self.model_config.embedding_base_url,
            timeout=self.model_config.llm_timeout
        )

        logger.info(f"Embedding客户端初始化完成: {self.model_config.embedding_model_name} @ {self.model_config.embedding_base_url}")

    async def embed(self, texts: List[str]) -> List[List[float]]:
        """生成嵌入向量"""
        if not texts:
            return []

        logger.debug(f"生成嵌入向量: {len(texts)}个文本")

        response = await self.client.embeddings.create(
            model=self.model_config.embedding_model_name,
            input=texts
        )

        embeddings = [data.embedding for data in response.data]
        logger.debug(f"嵌入向量生成完成: {len(embeddings)}个向量, 维度: {len(embeddings[0]) if embeddings else 0}")

        return embeddings


# 全局客户端实例
_llm_client = LLMClient()
_embedding_client = EmbeddingClient()


# 便捷函数
async def call_llm(prompt: str, **kwargs) -> str:
    """调用LLM的便捷函数"""

    print('-'*50)
    print(prompt)
    print('-'*50)

    return await _llm_client.call(prompt, **kwargs)


async def call_llm_stream(prompt: str, **kwargs) -> AsyncGenerator[str, None]:
    """流式调用LLM的便捷函数"""

    print('-'*50)
    print(f"[STREAM] {prompt}")
    print('-'*50)

    async for chunk in _llm_client.call_stream(prompt, **kwargs):
        yield chunk


async def call_embedding(texts: List[str]) -> List[List[float]]:
    """调用Embedding的便捷函数"""
    return await _embedding_client.embed(texts)
