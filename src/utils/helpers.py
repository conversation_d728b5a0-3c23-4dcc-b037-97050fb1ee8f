import hashlib
from io import BytesIO
import logging
from typing import List, Dict, Tuple

from PIL import Image

logger = logging.getLogger(__name__)


def str_sha256(text: str) -> str:
    """计算字符串的SHA256哈希值"""
    return hashlib.sha256(text.encode('utf-8')).hexdigest()


def image_to_bytes(image: Image.Image, image_format: str = "JPEG") -> bytes:
    """将PIL图像转换为字节"""
    buffer = BytesIO()
    image.save(buffer, format=image_format)
    return buffer.getvalue()


def calculate_iou(box1: List[int], box2: List[int]) -> float:
    """计算两个边界框的IoU"""
    x1_1, y1_1, x2_1, y2_1 = box1
    x1_2, y1_2, x2_2, y2_2 = box2

    # 计算交集
    x1_inter = max(x1_1, x1_2)
    y1_inter = max(y1_1, y1_2)
    x2_inter = min(x2_1, x2_2)
    y2_inter = min(y2_1, y2_2)

    if x2_inter <= x1_inter or y2_inter <= y1_inter:
        return 0.0

    inter_area = (x2_inter - x1_inter) * (y2_inter - y1_inter)

    # 计算并集
    area1 = (x2_1 - x1_1) * (y2_1 - y1_1)
    area2 = (x2_2 - x1_2) * (y2_2 - y1_2)
    union_area = area1 + area2 - inter_area

    return inter_area / union_area if union_area > 0 else 0.0


def is_inside(small_box: List[int], big_box: List[int], overlap_threshold: float = 0.8) -> bool:
    """检查小框是否在大框内部"""
    x1_s, y1_s, x2_s, y2_s = small_box
    x1_b, y1_b, x2_b, y2_b = big_box

    # 计算重叠区域
    x1_overlap = max(x1_s, x1_b)
    y1_overlap = max(y1_s, y1_b)
    x2_overlap = min(x2_s, x2_b)
    y2_overlap = min(y2_s, y2_b)

    if x2_overlap <= x1_overlap or y2_overlap <= y1_overlap:
        return False

    overlap_area = (x2_overlap - x1_overlap) * (y2_overlap - y1_overlap)
    small_area = (x2_s - x1_s) * (y2_s - y1_s)

    return (overlap_area / small_area) >= overlap_threshold if small_area > 0 else False


def validate_bbox(bbox: List[int], image_size: Tuple[int, int]) -> bool:
    """验证边界框是否有效"""
    if len(bbox) != 4:
        logger.warning(f"Invalid bbox format: {bbox}. Expected 4 elements.")
        return False

    x1, y1, x2, y2 = bbox
    width, height = image_size

    # 检查坐标是否有效
    if x1 >= x2 or y1 >= y2:
        logger.warning(f"Invalid bbox coordinates: {bbox}. x1 must be < x2 and y1 must be < y2.")
        return False

    # 检查是否在图像范围内
    if x1 < 0 or y1 < 0 or x2 > width or y2 > height:
        logger.warning(f"Bbox {bbox} is out of image bounds {image_size}.")
        return False

    # 检查面积是否太小
    area = (x2 - x1) * (y2 - y1)
    if area < 100:  # 最小面积阈值
        logger.warning(f"Bbox {bbox} area {area} is too small (min 100).")
        return False

    return True


def merge_high_iou_tables(tables: List[Dict], iou_threshold: float = 0.3) -> List[Dict]:
    """合并高IoU的表格 - 降低阈值以更积极地合并相邻表格"""
    if not tables:
        return []

    merged = []
    used = set()
    logger.info(f"Merging high IOU tables. Initial count: {len(tables)}")

    for i, table1 in enumerate(tables):
        if i in used:
            continue

        bbox1 = table1["bbox"]
        merged_bbox = bbox1[:]
        merged_score = table1["score"]
        count = 1

        for j, table2 in enumerate(tables[i + 1:], i + 1):
            if j in used:
                continue

            bbox2 = table2["bbox"]
            iou = calculate_iou(bbox1, bbox2)

            if iou > iou_threshold:
                logger.debug(f"Merging tables with IOU {iou:.2f}: {bbox1} and {bbox2}")
                # 合并边界框
                merged_bbox[0] = min(merged_bbox[0], bbox2[0])  # x1
                merged_bbox[1] = min(merged_bbox[1], bbox2[1])  # y1
                merged_bbox[2] = max(merged_bbox[2], bbox2[2])  # x2
                merged_bbox[3] = max(merged_bbox[3], bbox2[3])  # y2

                # 平均分数
                merged_score = (merged_score * count + table2["score"]) / (count + 1)
                count += 1
                used.add(j)

        merged.append({
            "bbox": merged_bbox,
            "category": table1["category"],
            "score": merged_score
        })
        used.add(i)
    logger.info(f"Finished merging high IOU tables. Final count: {len(merged)}")
    return merged


def merge_adjacent_tables(tables: List[Dict], distance_threshold: int = 50) -> List[Dict]:
    """合并相邻的表格 - 基于距离而非IoU"""
    if not tables:
        return []

    merged = []
    used = set()
    logger.info(f"Merging adjacent tables. Initial count: {len(tables)}")

    for i, table1 in enumerate(tables):
        if i in used:
            continue

        bbox1 = table1["bbox"]
        x1_1, y1_1, x2_1, y2_1 = bbox1
        merged_bbox = bbox1[:]
        merged_score = table1["score"]
        count = 1

        for j, table2 in enumerate(tables[i + 1:], i + 1):
            if j in used:
                continue

            bbox2 = table2["bbox"]
            x1_2, y1_2, x2_2, y2_2 = bbox2

            # 检查是否相邻（水平或垂直相邻）
            is_adjacent = False

            # 水平相邻检查
            if (abs(x2_1 - x1_2) < distance_threshold or abs(x2_2 - x1_1) < distance_threshold):
                # 检查垂直重叠
                y_overlap = min(y2_1, y2_2) - max(y1_1, y1_2)
                if y_overlap > 0:
                    is_adjacent = True

            # 垂直相邻检查
            if (abs(y2_1 - y1_2) < distance_threshold or abs(y2_2 - y1_1) < distance_threshold):
                # 检查水平重叠
                x_overlap = min(x2_1, x2_2) - max(x1_1, x1_2)
                if x_overlap > 0:
                    is_adjacent = True

            if is_adjacent:
                logger.debug(f"Merging adjacent tables: {bbox1} and {bbox2}")
                # 合并边界框
                merged_bbox[0] = min(merged_bbox[0], bbox2[0])  # x1
                merged_bbox[1] = min(merged_bbox[1], bbox2[1])  # y1
                merged_bbox[2] = max(merged_bbox[2], bbox2[2])  # x2
                merged_bbox[3] = max(merged_bbox[3], bbox2[3])  # y2

                # 平均分数
                merged_score = (merged_score * count + table2["score"]) / (count + 1)
                count += 1
                used.add(j)

        merged.append({
            "bbox": merged_bbox,
            "category": table1["category"],
            "score": merged_score
        })
        used.add(i)
    logger.info(f"Finished merging adjacent tables. Final count: {len(merged)}")
    return merged


def filter_nested_tables(tables: List[Dict], overlap_threshold: float = 0.7) -> List[Dict]:
    """过滤嵌套表格 - 保留大表格，移除被包含的小表格"""
    if not tables:
        return []

    logger.info(f"Filtering nested tables. Initial count: {len(tables)}")
    # 按面积排序，大的在前
    sorted_tables = sorted(tables, key=lambda t: (t["bbox"][2] - t["bbox"][0]) * (t["bbox"][3] - t["bbox"][1]), reverse=True)

    filtered = []

    for i, table in enumerate(sorted_tables):
        is_contained = False

        # 检查这个表格是否被已经选择的更大表格包含
        for selected_table in filtered:
            if is_inside(table["bbox"], selected_table["bbox"], overlap_threshold):
                is_contained = True
                logger.debug(f"Filtering out nested table {table["bbox"]} inside {selected_table["bbox"]}")
                break

        # 如果没有被包含，则保留
        if not is_contained:
            filtered.append(table)
    logger.info(f"Finished filtering nested tables. Final count: {len(filtered)}")
    return filtered


def prefer_large_tables(tables: List[Dict], min_area: int = 10000) -> List[Dict]:
    """优先选择大表格，过滤掉过小的表格"""
    if not tables:
        return []

    filtered = []
    initial_count = len(tables)
    for table in tables:
        bbox = table["bbox"]
        area = (bbox[2] - bbox[0]) * (bbox[3] - bbox[1])
        if area >= min_area:
            filtered.append(table)
        else:
            logger.debug(f"Filtering out small table {bbox} with area {area} (min_area: {min_area})")
    logger.info(f"Filtered {initial_count - len(filtered)} small tables. Remaining: {len(filtered)}")
    return filtered


def filter_full_page_tables(tables: List[Dict], image_size: Tuple[int, int],
                            coverage_threshold: float = 0.8) -> List[Dict]:
    """过滤掉覆盖整个页面的表格"""
    if not tables:
        return []

    image_width, image_height = image_size
    image_area = image_width * image_height

    filtered = []
    initial_count = len(tables)
    for table in tables:
        bbox = table["bbox"]
        table_area = (bbox[2] - bbox[0]) * (bbox[3] - bbox[1])

        # 如果表格覆盖了页面的大部分区域，可能是误检
        coverage_ratio = table_area / image_area
        if coverage_ratio < coverage_threshold:
            filtered.append(table)
        else:
            logger.debug(f"Filtering out full page table {bbox} with coverage {coverage_ratio:.2f} (threshold: {coverage_threshold})")

    logger.info(f"Filtered {initial_count - len(filtered)} full page tables. Remaining: {len(filtered)}")
    return filtered


def smart_table_filter(tables: List[Dict], image_size: Tuple[int, int]) -> List[Dict]:
    """智能表格过滤 - 综合多种条件"""
    if not tables:
        return []

    logger.info(f"Applying smart table filter. Initial count: {len(tables)}")

    # 1. 过滤掉太小的表格
    tables = prefer_large_tables(tables, min_area=1000)

    # 2. 对于大表格，我们保留它们，因为可能确实是完整的表格
    # 只过滤掉明显异常的情况（比如完全覆盖页面边缘的表格）
    filtered = []
    for table in tables:
        bbox = table["bbox"]
        x1, y1, x2, y2 = bbox

        # 检查是否是页面边缘的表格（可能是误检）
        margin = 10  # 边缘容忍度
        is_edge_table = (x1 <= margin and y1 <= margin and
                         x2 >= image_size[0] - margin and
                         y2 >= image_size[1] - margin)

        if not is_edge_table:
            filtered.append(table)
        else:
            # 即使是边缘表格，如果置信度很高，也保留
            if table.get("score", 0) > 0.8:
                filtered.append(table)
            else:
                logger.debug(f"Filtering out edge table {bbox} with low confidence {table.get("score", 0):.2f}")

    # 3. 按面积排序，保留最大的几个
    filtered = sorted(filtered, key=lambda t: (t["bbox"][2] - t["bbox"][0]) * (t["bbox"][3] - t["bbox"][1]), reverse=True)

    # 4. 最多保留10个最大的表格
    final_tables = filtered[:10]
    logger.info(f"Smart table filter completed. Final count: {len(final_tables)}")
    return final_tables


def refine_table_boundaries(tables: List[Dict], image_size: Tuple[int, int]) -> List[Dict]:
    """精细化表格边界 - 避免表格覆盖整个页面"""
    if not tables:
        return []

    refined = []
    logger.info(f"Refining table boundaries. Initial count: {len(tables)}")
    for table in tables:
        bbox = table["bbox"]
        x1, y1, x2, y2 = bbox

        # 如果表格覆盖了整个页面，尝试收缩边界
        if (x1 <= 50 and y1 <= 50 and
                x2 >= image_size[0] - 50 and
                y2 >= image_size[1] - 50):

            # 收缩边界，留出页边距
            margin = min(200, image_size[0] // 20, image_size[1] // 20)
            new_x1 = max(margin, x1)
            new_y1 = max(margin, y1)
            new_x2 = min(image_size[0] - margin, x2)
            new_y2 = min(image_size[1] - margin, y2)

            # 确保新边界框仍然有效
            if new_x2 > new_x1 + 100 and new_y2 > new_y1 + 100:
                refined_table = table.copy()
                refined_table["bbox"] = [new_x1, new_y1, new_x2, new_y2]
                refined.append(refined_table)
                logger.debug(f"Refined table bbox from {bbox} to {[new_x1, new_y1, new_x2, new_y2]}")
            else:
                refined.append(table)  # 如果收缩后太小，保持原样
                logger.debug(f"Could not refine table bbox {bbox}, keeping original.")
        else:
            refined.append(table)

    logger.info(f"Finished refining table boundaries. Final count: {len(refined)}")
    return refined


def filter_by_content_analysis(tables: List[Dict], image: Image.Image) -> List[Dict]:
    """
    基于内容分析过滤表格

    Args:
        tables: 检测到的表格列表
        image: 原始图像

    Returns:
        过滤后的表格列表
    """
    import cv2
    import numpy as np

    filtered_tables = []
    initial_count = len(tables)
    logger.info(f"Applying content analysis filter. Initial count: {initial_count}")

    for table in tables:
        bbox = table["bbox"]
        x1, y1, x2, y2 = bbox

        # 裁剪区域
        crop = image.crop((x1, y1, x2, y2))
        img_array = np.array(crop)

        if len(img_array.shape) == 3:
            gray = cv2.cvtColor(img_array, cv2.COLOR_RGB2GRAY)
        else:
            gray = img_array

        height, width = gray.shape

        # 1. 检测白色背景比例（表格通常有大量白色背景）
        white_pixels = np.sum(gray > 240)
        white_ratio = white_pixels / (height * width)

        # 2. 检测黑色线条（表格边框）
        black_pixels = np.sum(gray < 50)
        black_ratio = black_pixels / (height * width)

        # 3. 检测中间色调（图片特征）
        middle_pixels = np.sum((gray > 100) & (gray < 200))
        middle_ratio = middle_pixels / (height * width)

        # 4. 面积检查
        area = (x2 - x1) * (y2 - y1)

        # 判断逻辑：
        # 表格特征：大量白色背景 + 少量黑色线条 + 较少中间色调
        # 图片特征：较少白色背景 + 大量中间色调

        is_likely_table = (
                white_ratio > 0.6 and  # 大量白色背景
                black_ratio > 0.01 and  # 有一定的黑色线条
                middle_ratio < 0.3 and  # 较少中间色调
                area > 3000  # 足够大的区域
        )

        if is_likely_table:
            filtered_tables.append(table)
        else:
            logger.debug(f"Filtering out non-table region {bbox} based on content analysis. White: {white_ratio:.2f}, Black: {black_ratio:.2f}, Middle: {middle_ratio:.2f}, Area: {area}")

    logger.info(f"Content analysis filter: Filtered {initial_count - len(filtered_tables)} regions, kept {len(filtered_tables)} tables.")

    return filtered_tables


def has_table_structure(image_crop) -> bool:
    """
    检测图像是否具有表格结构

    Args:
        image_crop: 裁剪的图像区域

    Returns:
        是否具有表格结构
    """
    import cv2
    import numpy as np

    # 转换为numpy数组
    img_array = np.array(image_crop)
    if len(img_array.shape) == 3:
        gray = cv2.cvtColor(img_array, cv2.COLOR_RGB2GRAY)
    else:
        gray = img_array

    height, width = gray.shape

    # 1. 检测水平线
    horizontal_kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (width // 4, 1))
    horizontal_lines = cv2.morphologyEx(gray, cv2.MORPH_OPEN, horizontal_kernel)

    # 2. 检测垂直线
    vertical_kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (1, height // 4))
    vertical_lines = cv2.morphologyEx(gray, cv2.MORPH_OPEN, vertical_kernel)

    # 3. 合并线条
    table_mask = cv2.add(horizontal_lines, vertical_lines)

    # 4. 查找线条交点（表格特征）
    intersections = cv2.bitwise_and(horizontal_lines, vertical_lines)
    intersection_count = np.sum(intersections > 0)

    # 5. 计算线条密度
    line_pixels = np.sum(table_mask > 0)
    line_density = line_pixels / (height * width)

    # 6. 检测网格结构
    # 查找轮廓来检测单元格
    contours, _ = cv2.findContours(table_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

    # 计算矩形轮廓的数量
    rectangular_contours = 0
    for contour in contours:
        # 近似轮廓
        epsilon = 0.02 * cv2.arcLength(contour, True)
        approx = cv2.approxPolyDP(contour, epsilon, True)

        # 检查是否为矩形（4个顶点）
        if len(approx) == 4:
            rectangular_contours += 1

    # 判断是否为表格：
    # 1. 有足够的线条密度
    # 2. 有线条交点
    # 3. 有矩形结构
    has_structure = (
            line_density > 0.005 and  # 有一定的线条密度
            intersection_count > 2 and  # 有多个交点
            rectangular_contours > 1  # 有多个矩形结构
    )
    logger.debug(f"Table structure analysis: line_density={line_density:.4f}, intersection_count={intersection_count}, rectangular_contours={rectangular_contours}, has_structure={has_structure}")
    return has_structure


def is_likely_image(image_crop, bbox: List[int]) -> bool:
    """
    简单快速地检测是否为图片

    Args:
        image_crop: 裁剪的图像区域
        bbox: 边界框坐标

    Returns:
        是否为图片
    """
    import cv2
    import numpy as np

    # 转换为numpy数组
    img_array = np.array(image_crop)
    if len(img_array.shape) == 3:
        gray = cv2.cvtColor(img_array, cv2.COLOR_RGB2GRAY)
    else:
        gray = img_array

    height, width = gray.shape

    # 1. 检查是否有大量连续的非白色区域（图片特征）
    # 计算非白色像素比例
    non_white_pixels = np.sum(gray < 240)
    non_white_ratio = non_white_pixels / (height * width)

    # 2. 检查颜色变化的平滑度（图片通常有平滑的颜色渐变）
    # 计算梯度
    grad_x = cv2.Sobel(gray, cv2.CV_64F, 1, 0, ksize=3)
    grad_y = cv2.Sobel(gray, cv2.CV_64F, 0, 1, ksize=3)
    gradient_magnitude = np.sqrt(grad_x ** 2 + grad_y ** 2)

    # 计算平滑区域（低梯度区域）的比例
    smooth_pixels = np.sum(gradient_magnitude < 10)
    smooth_ratio = smooth_pixels / (height * width)

    # 3. 检查是否有明显的线条结构（表格特征）
    # 水平线检测
    horizontal_kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (max(width // 20, 5), 1))
    horizontal_lines = cv2.morphologyEx(gray, cv2.MORPH_OPEN, horizontal_kernel)
    horizontal_pixels = np.sum(horizontal_lines > 0)

    # 垂直线检测
    vertical_kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (1, max(height // 20, 5)))
    vertical_lines = cv2.morphologyEx(gray, cv2.MORPH_OPEN, vertical_kernel)
    vertical_pixels = np.sum(vertical_lines > 0)

    line_pixels = horizontal_pixels + vertical_pixels
    line_ratio = line_pixels / (height * width)

    # 4. 面积检查
    area = (bbox[2] - bbox[0]) * (bbox[3] - bbox[1])

    # 判断逻辑：
    # 如果满足以下条件，很可能是图片：
    # - 大量非白色像素 AND 大量平滑区域 AND 很少线条结构
    is_image = (
            non_white_ratio > 0.3 and  # 大量非白色内容
            smooth_ratio > 0.7 and  # 大量平滑区域
            line_ratio < 0.01 and  # 很少线条结构
            area > 5000  # 足够大的区域
    )
    logger.debug(f"Image likelihood analysis: non_white_ratio={non_white_ratio:.2f}, smooth_ratio={smooth_ratio:.2f}, line_ratio={line_ratio:.4f}, area={area}, is_image={is_image}")
    return is_image


def detect_image_vs_table(image_crop, bbox: List[int]) -> Dict:
    """
    检测裁剪区域是图片还是表格

    Args:
        image_crop: 裁剪的图像区域
        bbox: 边界框坐标

    Returns:
        包含检测结果的字典
    """
    import cv2
    import numpy as np

    # 转换为numpy数组
    img_array = np.array(image_crop)
    if len(img_array.shape) == 3:
        gray = cv2.cvtColor(img_array, cv2.COLOR_RGB2GRAY)
    else:
        gray = img_array

    height, width = gray.shape
    total_pixels = height * width

    # 1. 检测线条结构（表格特征）
    # 水平线检测
    horizontal_kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (width // 10, 1))
    horizontal_lines = cv2.morphologyEx(gray, cv2.MORPH_OPEN, horizontal_kernel)
    horizontal_pixels = np.sum(horizontal_lines > 0)

    # 垂直线检测
    vertical_kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (1, height // 10))
    vertical_lines = cv2.morphologyEx(gray, cv2.MORPH_OPEN, vertical_kernel)
    vertical_pixels = np.sum(vertical_lines > 0)

    # 线条密度
    line_density = (horizontal_pixels + vertical_pixels) / total_pixels

    # 2. 检测边缘密度
    edges = cv2.Canny(gray, 50, 150)
    edge_density = np.sum(edges > 0) / total_pixels

    # 3. 检测颜色复杂度（图片通常颜色更丰富）
    if len(img_array.shape) == 3:
        # 计算颜色直方图的熵
        hist_r = cv2.calcHist([img_array], [0], None, [256], [0, 256])
        hist_g = cv2.calcHist([img_array], [1], None, [256], [0, 256])
        hist_b = cv2.calcHist([img_array], [2], None, [256], [0, 256])

        # 计算熵
        def calculate_entropy(hist):
            hist = hist.flatten()
            hist = hist[hist > 0]
            prob = hist / np.sum(hist)
            return -np.sum(prob * np.log2(prob))

        color_entropy = (calculate_entropy(hist_r) +
                         calculate_entropy(hist_g) +
                         calculate_entropy(hist_b)) / 3
    else:
        hist = cv2.calcHist([gray], [0], None, [256], [0, 256])
        hist = hist.flatten()
        hist = hist[hist > 0]
        prob = hist / np.sum(hist)
        color_entropy = -np.sum(prob * np.log2(prob))

    # 4. 检测文本区域（表格通常包含文本）
    # 使用形态学操作检测文本块
    kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (3, 3))
    morph = cv2.morphologyEx(gray, cv2.MORPH_CLOSE, kernel)

    # 查找轮廓
    contours, _ = cv2.findContours(morph, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

    # 计算小轮廓的数量（可能是文字）
    small_contours = 0
    for contour in contours:
        area = cv2.contourArea(contour)
        if 10 < area < 1000:  # 文字大小的轮廓
            small_contours += 1

    text_density = small_contours / (total_pixels / 1000)  # 标准化

    # 5. 长宽比分析
    aspect_ratio = width / height if height > 0 else 1

    # 综合评分
    table_score = 0

    # 线条结构权重（表格应该有明显的线条）
    if line_density > 0.02:
        table_score += 4  # 强表格特征
    elif line_density > 0.01:
        table_score += 2
    elif line_density > 0.005:
        table_score += 1
    else:
        table_score -= 1  # 没有线条结构

    # 边缘密度权重（表格边缘相对简单）
    if 0.05 < edge_density < 0.25:
        table_score += 2
    elif edge_density > 0.6:
        table_score -= 3  # 太多边缘，很可能是图片
    elif edge_density < 0.02:
        table_score -= 1  # 太少边缘

    # 颜色复杂度权重（表格颜色相对简单）
    if color_entropy < 3:
        table_score += 3  # 简单颜色，很可能是表格
    elif color_entropy < 5:
        table_score += 1
    elif color_entropy > 7:
        table_score -= 3  # 复杂颜色，很可能是图片

    # 文本密度权重（表格应该包含文本）
    if text_density > 1.0:
        table_score += 3  # 大量文本，很可能是表格
    elif text_density > 0.5:
        table_score += 2
    elif text_density < 0.1:
        table_score -= 2  # 几乎没有文本

    # 长宽比权重（极端长宽比可能不是表格）
    if 0.3 < aspect_ratio < 3:
        table_score += 1
    elif aspect_ratio > 10 or aspect_ratio < 0.1:
        table_score -= 2  # 极端长宽比

    # 尺寸权重（太小的区域可能不是表格）
    area = (bbox[2] - bbox[0]) * (bbox[3] - bbox[1])
    if area > 10000:
        table_score += 2  # 大区域更可能是表格
    elif area > 5000:
        table_score += 1
    elif area < 2000:
        table_score -= 2  # 太小的区域

    # 更严格的判断标准
    is_table = table_score > 3  # 提高阈值
    confidence = min(max(table_score / 12, 0), 1)  # 调整置信度计算

    logger.debug(f"Image vs Table detection: bbox={bbox}, score={table_score}, is_table={is_table}, confidence={confidence:.2f}")
    return {
        "is_table": is_table,
        "confidence": confidence,
        "line_density": line_density,
        "edge_density": edge_density,
        "color_entropy": color_entropy,
        "text_density": text_density,
        "aspect_ratio": aspect_ratio,
        "area": area,
        "table_score": table_score
    }


def filter_images_from_tables(tables: List[Dict], image: Image.Image, debug: bool = False) -> List[Dict]:
    """
    过滤掉被误识别为表格的图片

    Args:
        tables: 检测到的表格列表
        image: 原始图像
        debug: 是否输出调试信息

    Returns:
        过滤后的表格列表
    """
    filtered_tables = []
    filtered_count = 0
    initial_count = len(tables)
    logger.info(f"Applying image filter to tables. Initial count: {initial_count}")

    for i, table in enumerate(tables):
        bbox = table["bbox"]
        x1, y1, x2, y2 = bbox

        # 裁剪区域
        crop = image.crop((x1, y1, x2, y2))

        # 首先检查是否具有表格结构
        has_structure = has_table_structure(crop)

        if not has_structure:
            # 如果没有表格结构，进一步检查是否为图片
            is_image = is_likely_image(crop, bbox)

            if is_image:
                filtered_count += 1
                logger.debug(f"Region {i}: Bbox {bbox} - detected as image, filtered out.")
                continue

        # 如果不是明显的图片，再使用详细的表格检测
        detection_result = detect_image_vs_table(crop, bbox)

        if debug:
            logger.debug(f"Region {i}: Bbox {bbox}")
            logger.debug(f"  Table Score: {detection_result['table_score']}")
            logger.debug(f"  Is Table: {detection_result['is_table']}")
            logger.debug(f"  Confidence: {detection_result['confidence']:.2f}")
            logger.debug(f"  Line Density: {detection_result['line_density']:.4f}")
            logger.debug(f"  Edge Density: {detection_result['edge_density']:.4f}")
            logger.debug(f"  Color Entropy: {detection_result['color_entropy']:.2f}")
            logger.debug(f"  Text Density: {detection_result['text_density']:.2f}")
            logger.debug(f"  Aspect Ratio: {detection_result['aspect_ratio']:.2f}")
            logger.debug(f"  Area: {detection_result['area']}")

        if detection_result["is_table"]:
            # 添加检测信息到表格数据
            table["detection_info"] = detection_result
            filtered_tables.append(table)
        else:
            filtered_count += 1
            logger.debug(f"Region {i}: Bbox {bbox} - detected as non-table, filtered out.")

    logger.info(f"Image filter: Filtered {filtered_count} image regions, kept {len(filtered_tables)} tables.")

    return filtered_tables
