import glob
import json
import os
import re
import urllib.request
import logging
from html.parser import HTMLParser

logger = logging.getLogger(__name__)


class HTMLTableParser(HTMLParser):
    """HTML表格解析器"""
    
    def __init__(self):
        super().__init__()
        self.tables = []
        self.current_table = None
        self.current_row = None
        self.current_cell = None
        self.in_table = False
        self.in_row = False
        self.in_cell = False
        self.in_header = False
        self.cell_data = ""
        
    def handle_starttag(self, tag, attrs):
        if tag == 'table':
            self.in_table = True
            self.current_table = {
                'headers': [],
                'rows': [],
                'has_header': False
            }
        elif tag == 'tr' and self.in_table:
            self.in_row = True
            self.current_row = []
        elif tag in ['td', 'th'] and self.in_row:
            self.in_cell = True
            self.in_header = (tag == 'th')
            self.cell_data = ""
            
            # 处理colspan和rowspan属性
            colspan = 1
            rowspan = 1
            for attr_name, attr_value in attrs:
                if attr_name == 'colspan':
                    colspan = int(attr_value) if attr_value.isdigit() else 1
                elif attr_name == 'rowspan':
                    rowspan = int(attr_value) if attr_value.isdigit() else 1
            
            self.current_cell = {
                'data': '',
                'colspan': colspan,
                'rowspan': rowspan,
                'is_header': self.in_header
            }
    
    def handle_endtag(self, tag):
        if tag == 'table' and self.in_table:
            self.in_table = False
            if self.current_table:
                self.tables.append(self.current_table)
                self.current_table = None
        elif tag == 'tr' and self.in_row:
            self.in_row = False
            if self.current_row:
                if self.in_header or any(cell.get('is_header', False) for cell in self.current_row):
                    self.current_table['headers'] = [cell['data'].strip() for cell in self.current_row]
                    self.current_table['has_header'] = True
                else:
                    self.current_table['rows'].append(self.current_row)
                self.current_row = None
        elif tag in ['td', 'th'] and self.in_cell:
            self.in_cell = False
            self.in_header = False
            if self.current_cell:
                self.current_cell['data'] = self.cell_data.strip()
                if self.current_row is not None:
                    self.current_row.append(self.current_cell)
                self.current_cell = None
                self.cell_data = ""
    
    def handle_data(self, data):
        if self.in_cell:
            self.cell_data += data

def clean_text(text):
    """清理文本内容"""
    # 移除多余的空白字符
    text = re.sub(r'\s+', ' ', text)
    # 移除HTML实体
    text = text.replace('&nbsp;', ' ')
    text = text.replace('&amp;', '&')
    text = text.replace('&lt;', '<')
    text = text.replace('&gt;', '>')
    text = text.replace('&quot;', '"')
    return text.strip()

def convert_value(value):
    """尝试转换值的类型"""
    if not value:
        return ""
    
    # 清理文本
    value = clean_text(value)
    
    # 尝试转换为数字
    if value.isdigit():
        return int(value)
    
    # 尝试转换为浮点数
    try:
        if '.' in value and value.replace('.', '').replace('-', '').isdigit():
            return float(value)
    except ValueError:
        pass
    
    # 尝试转换百分比
    if value.endswith('%'):
        try:
            return float(value[:-1])
        except ValueError:
            pass
    
    return value

def table_to_json(table_data):
    """将表格数据转换为JSON数组"""
    if not table_data['rows']:
        return []
    
    headers = table_data['headers']
    rows = table_data['rows']
    
    # 如果没有表头，生成默认表头
    if not headers:
        max_cols = max(len(row) for row in rows) if rows else 0
        headers = [f"column_{i+1}" for i in range(max_cols)]
    
    json_array = []
    
    for row in rows:
        row_obj = {}
        for i, header in enumerate(headers):
            if i < len(row):
                cell_value = row[i]['data'] if isinstance(row[i], dict) else str(row[i])
                row_obj[header] = convert_value(cell_value)
            else:
                row_obj[header] = ""
        json_array.append(row_obj)
    
    return json_array

def parse_html_file(file_path):
    """解析HTML文件"""
    with open(file_path, 'r', encoding='utf-8') as f:
        html_content = f.read()
    
    return parse_html_content(html_content)

def parse_html_url(url):
    """解析HTML URL"""
    with urllib.request.urlopen(url) as response:
        html_content = response.read().decode('utf-8')
    
    return parse_html_content(html_content)

def parse_html_content(html_content):
    """解析HTML内容"""
    parser = HTMLTableParser()
    parser.feed(html_content)
    return parser.tables

def process_single_html_file(input_file, output_file=None, pretty_print=True, table_index=0, format_type='object'):
    """
    处理单个HTML文件并转换为JSON

    Args:
        input_file (str): 输入的HTML文件路径
        output_file (str): 输出的JSON文件路径（可选）
        pretty_print (bool): 是否格式化JSON输出
        table_index (int): 要处理的表格索引（默认为第一个，-1表示所有表格）
        format_type (str): 输出格式类型 ('object' 或 'array')

    Returns:
        bool: 处理是否成功
    """
    try:
        tables = parse_html_file(input_file)

        if not tables:
            logger.warning(f"No tables found in file {input_file}")
            return False

        if table_index == -1:
            all_tables_data = []
            for i, table in enumerate(tables):
                table_json = table_to_json(table)
                if table_json:
                    if format_type == 'array':
                        headers = table['headers'] if table['headers'] else [f"column_{j+1}" for j in range(len(table_json[0]) if table_json else 0)]
                        table_array = [headers] + [[row[header] for header in headers] for row in table_json]
                        all_tables_data.append(table_array)
                    else:
                        all_tables_data.append(table_json)

            json_data = all_tables_data
        else:
            if table_index >= len(tables):
                logger.warning(f"Table index {table_index} out of range, found {len(tables)} tables")
                return False

            table_json = table_to_json(tables[table_index])
            if not table_json:
                logger.warning(f"Table {table_index + 1} has no data")
                return False

            if format_type == 'array':
                headers = tables[table_index]['headers'] if tables[table_index]['headers'] else [f"column_{j+1}" for j in range(len(table_json[0]) if table_json else 0)]
                json_data = [headers] + [[row[header] for header in headers] for row in table_json]
            else:
                json_data = table_json

        if pretty_print:
            json_output = json.dumps(json_data, ensure_ascii=False, indent=2)
        else:
            json_output = json.dumps(json_data, ensure_ascii=False)

        if output_file:
            os.makedirs(os.path.dirname(output_file), exist_ok=True)
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(json_output)

        return True

    except FileNotFoundError:
        logger.error(f"Error: File '{input_file}' not found")
        return False
    except Exception as e:
        logger.error(f"Error processing file '{input_file}': {e}")
        return False

def batch_convert_html_to_json(input_dir, format_type='object', pretty_print=True, table_index=-1):
    """
    批量转换HTML文件到JSON，保存在同目录下

    Args:
        input_dir (str): 输入HTML文件目录
        format_type (str): 输出格式类型 ('object' 或 'array')
        pretty_print (bool): 是否格式化JSON输出
        table_index (int): 要处理的表格索引（-1表示所有表格）

    Returns:
        dict: 处理结果统计
    """
    if not os.path.exists(input_dir):
        logger.error(f"Input directory does not exist: {input_dir}")
        return {'success': 0, 'failed': 0, 'total': 0}

    html_patterns = ['*.html', '*.htm']
    html_files = []

    for pattern in html_patterns:
        html_files.extend(glob.glob(os.path.join(input_dir, '**', pattern), recursive=True))

    if not html_files:
        logger.warning(f"No HTML files found in directory {input_dir}")
        return {'success': 0, 'failed': 0, 'total': 0}

    logger.info(f"Found {len(html_files)} HTML files.")
    logger.info(f"Processing directory: {input_dir}")
    logger.info(f"Format type: {format_type}")
    logger.info(f"Table index: {'All tables' if table_index == -1 else f'Table {table_index+1}'}")
    logger.info(f"JSON files will be saved in the same directory as HTML files.")

    success_count = 0
    failed_count = 0

    for i, html_file in enumerate(html_files, 1):
        logger.info(f"[{i}/{len(html_files)}] Processing: {os.path.basename(html_file)}")

        html_dir = os.path.dirname(html_file)
        html_basename = os.path.basename(html_file)
        json_filename = os.path.splitext(html_basename)[0] + '.json'
        output_file = os.path.join(html_dir, json_filename)

        success = process_single_html_file(
            html_file,
            output_file,
            pretty_print,
            table_index,
            format_type
        )

        if success:
            success_count += 1
            logger.info(f"Successfully converted: {output_file}")
        else:
            failed_count += 1
            logger.error(f"Failed to convert: {html_file}")

    logger.info(f"\n{'='*60}")
    logger.info(f"Batch conversion completed!")
    logger.info(f"Success: {success_count} files")
    logger.info(f"Failed: {failed_count} files")
    logger.info(f"Total: {len(html_files)} files")
    logger.info(f"JSON files saved in respective HTML file directories.")

    return {
        'success': success_count,
        'failed': failed_count,
        'total': len(html_files)
    }
