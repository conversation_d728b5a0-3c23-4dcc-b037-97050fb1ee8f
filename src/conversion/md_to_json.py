import re
import json
import os
import glob
import logging
from typing import List, Dict, Any

logger = logging.getLogger(__name__)


class MarkdownTableParser:
    """Markdown表格解析器"""
    
    def __init__(self):
        # 匹配表格行的正则表达式
        self.table_row_pattern = re.compile(r'^\s*\|(.+)\s*
)
        # 匹配分隔行的正则表达式
        self.separator_pattern = re.compile(r'^\s*\|[\s\-\:\|]+\s*
)
    
    def parse_file(self, file_path: str) -> List[Dict[str, Any]]:
        """
        解析Markdown文件中的所有表格
        
        Args:
            file_path: Markdown文件路径
            
        Returns:
            包含所有表格的列表
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
        except UnicodeDecodeError:
            logger.warning(f"UnicodeDecodeError with utf-8 for {file_path}, trying gbk.")
            with open(file_path, 'r', encoding='gbk') as f:
                content = f.read()
        
        lines = content.split('\n')
        tables = []
        
        i = 0
        while i < len(lines):
            # 查找表格开始
            if self._is_table_row(lines[i]):
                table, end_index = self._parse_table(lines, i)
                if table:
                    tables.append({
                        'table_index': len(tables),
                        'start_line': i + 1,
                        'end_line': end_index + 1,
                        'headers': table['headers'],
                        'rows': table['rows'],
                        'row_count': len(table['rows']),
                        'column_count': len(table['headers']) if table['headers'] else 0
                    })
                i = end_index + 1
            else:
                i += 1
        
        return tables
    
    def _is_table_row(self, line: str) -> bool:
        """判断是否是表格行"""
        return bool(self.table_row_pattern.match(line))
    
    def _is_separator_row(self, line: str) -> bool:
        """判断是否是分隔行"""
        if not self.table_row_pattern.match(line):
            return False

        # 提取单元格内容
        match = self.table_row_pattern.match(line)
        cells_content = match.group(1)
        cells = [cell.strip() for cell in cells_content.split('|')]

        # 检查是否所有单元格都只包含分隔符
        for cell in cells:
            if cell and not re.match(r'^[\s\-\:]+
, cell):
                return False

        return True
    
    def _parse_table(self, lines: List[str], start_index: int) -> tuple:
        """
        解析单个表格
        
        Args:
            lines: 所有行
            start_index: 表格开始行索引
            
        Returns:
            (表格数据, 结束行索引)
        """
        if start_index >= len(lines):
            return None, start_index
        
        # 解析表头
        header_line = lines[start_index]
        headers = self._parse_row(header_line)
        
        if not headers:
            return None, start_index
        
        current_index = start_index + 1
        
        # 检查是否有分隔行
        has_separator = False
        if (current_index < len(lines) and 
            self._is_separator_row(lines[current_index])):
            has_separator = True
            current_index += 1
        
        # 解析数据行
        rows = []
        while (current_index < len(lines) and
               self._is_table_row(lines[current_index])):
            # 跳过分隔行
            if self._is_separator_row(lines[current_index]):
                current_index += 1
                continue

            row_data = self._parse_row(lines[current_index])
            if row_data:
                # 确保行数据与表头列数一致
                while len(row_data) < len(headers):
                    row_data.append("")
                rows.append(row_data[:len(headers)])
            current_index += 1
        
        # 如果没有分隔行且只有一行，可能不是表格
        if not has_separator and len(rows) == 0:
            return None, start_index
        
        return {
            'headers': headers,
            'rows': rows
        }, current_index - 1
    
    def _parse_row(self, line: str) -> List[str]:
        """
        解析表格行
        """
        match = self.table_row_pattern.match(line)
        if not match:
            return []
        
        # 提取单元格内容
        cells_content = match.group(1)
        cells = [cell.strip() for cell in cells_content.split('|')]
        
        # 过滤空单元格（开头和结尾的）
        while cells and not cells[0]:
            cells.pop(0)
        while cells and not cells[-1]:
            cells.pop()
        
        return cells


def convert_to_json_objects(tables: List[Dict[str, Any]]) -> List[List[Dict[str, str]]]:
    """
    将表格转换为JSON对象数组
    
    Args:
        tables: 解析的表格列表
        
    Returns:
        JSON对象数组列表
    """
    json_tables = []
    
    for table in tables:
        headers = table['headers']
        rows = table['rows']
        
        # 将每行转换为对象
        json_rows = []
        for row in rows:
            row_obj = {}
            for i, header in enumerate(headers):
                value = row[i] if i < len(row) else ""
                row_obj[header] = value
            json_rows.append(row_obj)
        
        json_tables.append(json_rows)
    
    return json_tables


def convert_to_json_arrays(tables: List[Dict[str, Any]]) -> List[List[List[str]]]:
    """
    将表格转换为JSON数组
    
    Args:
        tables: 解析的表格列表
        
    Returns:
        JSON数组列表
    """
    json_tables = []
    
    for table in tables:
        headers = table['headers']
        rows = table['rows']
        
        # 包含表头的完整数组
        table_array = [headers] + rows
        json_tables.append(table_array)
    
    return json_tables


def process_single_md_file(input_file, output_file=None, format_type='array', pretty_print=True):
    """
    处理单个Markdown文件并转换为JSON

    Args:
        input_file (str): 输入的Markdown文件路径
        output_file (str): 输出的JSON文件路径（可选）
        format_type (str): 输出格式类型 ('object' 或 'array')
        pretty_print (bool): 是否格式化JSON输出

    Returns:
        bool: 处理是否成功
    """
    try:
        # 解析表格
        parser_obj = MarkdownTableParser()
        tables = parser_obj.parse_file(input_file)

        if not tables:
            logger.warning(f"No tables found in file {input_file}")
            return False

        # 转换为JSON
        if format_type == 'object':
            json_data = convert_to_json_objects(tables)
        else:
            json_data = convert_to_json_arrays(tables)

        # 格式化JSON
        if pretty_print:
            json_output = json.dumps(json_data, ensure_ascii=False, indent=2)
        else:
            json_output = json.dumps(json_data, ensure_ascii=False)

        # 输出结果
        if output_file:
            # 确保输出目录存在
            os.makedirs(os.path.dirname(output_file), exist_ok=True)
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(json_output)

        return True

    except FileNotFoundError:
        logger.error(f"Error: File '{input_file}' not found")
        return False
    except Exception as e:
        logger.error(f"Error processing file '{input_file}': {e}")
        return False


def batch_convert_md_to_json(input_dir, format_type='array', pretty_print=True):
    """
    批量转换Markdown文件到JSON，保存在同目录下

    Args:
        input_dir (str): 输入Markdown文件目录
        format_type (str): 输出格式类型 ('object' 或 'array')
        pretty_print (bool): 是否格式化JSON输出

    Returns:
        dict: 处理结果统计
    """
    if not os.path.exists(input_dir):
        logger.error(f"Input directory does not exist: {input_dir}")
        return {'success': 0, 'failed': 0, 'total': 0}

    md_patterns = ['*.md', '*.markdown']
    md_files = []

    for pattern in md_patterns:
        md_files.extend(glob.glob(os.path.join(input_dir, '**', pattern), recursive=True))

    if not md_files:
        logger.warning(f"No Markdown files found in directory {input_dir}")
        return {'success': 0, 'failed': 0, 'total': 0}

    logger.info(f"Found {len(md_files)} Markdown files.")
    logger.info(f"Processing directory: {input_dir}")
    logger.info(f"Format type: {format_type}")
    logger.info(f"JSON files will be saved in the same directory as Markdown files.")

    success_count = 0
    failed_count = 0

    for i, md_file in enumerate(md_files, 1):
        logger.info(f"[{i}/{len(md_files)}] Processing: {os.path.basename(md_file)}")

        md_dir = os.path.dirname(md_file)
        md_basename = os.path.basename(md_file)
        json_filename = os.path.splitext(md_basename)[0] + '.json'
        output_file = os.path.join(md_dir, json_filename)

        success = process_single_md_file(
            md_file,
            output_file,
            format_type,
            pretty_print
        )

        if success:
            success_count += 1
            logger.info(f"Successfully converted: {output_file}")
        else:
            failed_count += 1
            logger.error(f"Failed to convert: {md_file}")

    logger.info(f"\n{'='*60}")
    logger.info(f"Batch conversion completed!")
    logger.info(f"Success: {success_count} files")
    logger.info(f"Failed: {failed_count} files")
    logger.info(f"Total: {len(md_files)} files")
    logger.info(f"JSON files saved in respective Markdown file directories.")

    return {
        'success': success_count,
        'failed': failed_count,
        'total': len(md_files)
    }
