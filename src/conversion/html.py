import json
import os
import time
import logging
from typing import List, Dict

from src.config.settings import IMAGE_TO_HTML
from src.models.conversion import create_converter

logger = logging.getLogger(__name__)

class HTMLConverterManager:
    def __init__(self, config: Dict = None):
        self.config = config or IMAGE_TO_HTML
        self.converter = None
        self._init_converter()

    def _init_converter(self):
        if not self.config.get("enabled", True):
            logger.info("HTML conversion is disabled.")
            return

        try:
            self.converter = create_converter(
                model_path=self.config.get("model_path"),
                max_new_tokens=self.config.get("max_new_tokens"),
                max_time=self.config.get("max_time"),
                flash_attn=self.config.get("flash_attn"),
                device=self.config.get("device")
            )
            logger.info("HTML converter initialized successfully.")
        except Exception as e:
            logger.error(f"HTML converter initialization failed: {e}. HTML conversion will be skipped.")
            self.converter = None

    def is_enabled(self) -> bool:
        return self.converter is not None and self.config.get("enabled", True)

    def convert_images_to_html(self, image_files: List[str], output_dir: str = None) -> Dict:
        if not self.is_enabled():
            logger.warning("HTML conversion is not enabled.")
            return {
                "success": False,
                "message": "HTML conversion is not enabled",
                "converted_files": []
            }

        if not image_files:
            logger.info("No images to convert.")
            return {
                "success": True,
                "message": "No images to convert",
                "converted_files": []
            }

        if output_dir is None:
            output_dir = self.config.get("html_output_dir", "html_output")

        os.makedirs(output_dir, exist_ok=True)

        output_formats = self.config.get("output_formats", ["html"])
        save_files = self.config.get("save_html_files", True)

        converted_files = []
        conversion_results = []
        start_time = time.time()

        logger.info(f"Starting conversion of {len(image_files)} table images...")

        for i, image_file in enumerate(image_files):
            logger.info(f"Converting image {i + 1}/{len(image_files)}: {os.path.basename(image_file)}")

            base_name = os.path.splitext(os.path.basename(image_file))[0]
            file_results = {
                "source_image": image_file,
                "base_name": base_name,
                "formats": {}
            }

            for format_name in output_formats:
                try:
                    if format_name == "html":
                        result = self.converter.convert_to_html(image_file)
                    elif format_name == "markdown":
                        result = self.converter.convert_to_markdown(image_file)
                    elif format_name == "latex":
                        result = self.converter.convert_to_latex(image_file)
                    else:
                        logger.warning(f"Unsupported format: {format_name}")
                        continue

                    file_results["formats"][format_name] = {
                        "content": result,
                        "success": True
                    }

                    if save_files and result:
                        ext_map = {
                            "html": ".html",
                            "markdown": ".md",
                            "latex": ".tex"
                        }

                        output_file = os.path.join(
                            output_dir,
                            f"{base_name}{ext_map.get(format_name, '.txt')}"
                        )

                        with open(output_file, 'w', encoding='utf-8') as f:
                            if format_name == "html":
                                f.write(f"""<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>表格 - {base_name}</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; }}
        table {{ border-collapse: collapse; width: 100%; }}
        th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
        th {{ background-color: #f2f2f2; }}
    </style>
</head>
<body>
    <h1>表格内容</h1>
    <p>源图片: {os.path.basename(image_file)}</p>
    {result}
</body>
</html>""")
                            else:
                                f.write(result)

                        file_results["formats"][format_name]["output_file"] = output_file
                        converted_files.append(output_file)

                except Exception as e:
                    logger.error(f"Failed to convert format {format_name} for {os.path.basename(image_file)}: {e}")
                    file_results["formats"][format_name] = {
                        "content": "",
                        "success": False,
                        "error": str(e)
                    }

            conversion_results.append(file_results)

        end_time = time.time()
        total_time = round(end_time - start_time, 2)

        result_info = {
            "conversion_time": total_time,
            "total_images": len(image_files),
            "successful_conversions": len([r for r in conversion_results if "error" not in r]),
            "output_formats": output_formats,
            "output_directory": output_dir,
            "results": conversion_results
        }

        info_file = os.path.join(output_dir, "conversion_info.json")
        with open(info_file, 'w', encoding='utf-8') as f:
            json.dump(result_info, f, ensure_ascii=False, indent=2)

        logger.info(f"HTML conversion completed! Time taken: {total_time} seconds.")
        logger.info(f"Successfully converted: {result_info['successful_conversions']}/{len(image_files)} images.")
        if converted_files:
            logger.info(f"Output directory: {output_dir}")

        return {
            "success": True,
            "message": f"Conversion completed in {total_time} seconds",
            "converted_files": converted_files,
            "conversion_info": result_info
        }

    def convert_single_image(self, image_file: str, output_format: str = "html") -> str:
        if not self.is_enabled():
            raise RuntimeError("HTML conversion is not enabled")

        if output_format == "html":
            return self.converter.convert_to_html(image_file)
        elif output_format == "markdown":
            return self.converter.convert_to_markdown(image_file)
        elif output_format == "latex":
            return self.converter.convert_to_latex(image_file)
        else:
            raise ValueError(f"Unsupported output format: {output_format}")

    def get_supported_formats(self) -> List[str]:
        if self.converter:
            return self.converter.supported_output_format
        return []

    def update_config(self, new_config: Dict):
        self.config.update(new_config)
        self._init_converter()

_html_converter_manager = None

def get_html_converter_manager() -> HTMLConverterManager:
    global _html_converter_manager
    if _html_converter_manager is None:
        _html_converter_manager = HTMLConverterManager()
    return _html_converter_manager

def convert_images_to_html(image_files: List[str], output_dir: str = None) -> Dict:
    manager = get_html_converter_manager()
    return manager.convert_images_to_html(image_files, output_dir)

def convert_single_image_to_html(image_file: str, output_format: str = "html") -> str:
    manager = get_html_converter_manager()
    return manager.convert_single_image(image_file, output_format)
