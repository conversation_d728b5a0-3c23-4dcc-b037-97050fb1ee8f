# 安装指南

## 系统要求

- Python 3.8 或更高版本
- CUDA 支持的 GPU（推荐，用于加速推理）
- 至少 8GB 内存
- 至少 10GB 磁盘空间（用于模型文件）

## 安装方式

### 1. 使用 pip 安装（推荐）

```bash
# 基础安装
pip install -e .

# 或者安装所有可选依赖
pip install -e .[all]
```

### 2. 开发环境安装

```bash
# 克隆仓库
git clone <repository-url>
cd pdf-table-extraction

# 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或者
venv\Scripts\activate  # Windows

# 安装开发依赖
pip install -e .[dev]
```

### 3. 使用 conda 环境

```bash
# 创建 conda 环境
conda create -n pdf-table-extraction python=3.9
conda activate pdf-table-extraction

# 安装项目
pip install -e .
```

## 模型下载

项目需要以下模型文件：

1. **DocLayout-YOLO**: 用于文档布局检测
   - 路径: `models/DocLayout-YOLO-DocStructBench/`
   - 文件: `doclayout_yolo_docstructbench_imgsz1024.pt`

2. **SLANet+**: 用于表格结构识别
   - 路径: `models/SLANet_plus/`

3. **StructTable-InternVL2-1B**: 用于图像到HTML转换
   - 路径: `models/StructTable-InternVL2-1B/`

## 配置

### 环境变量配置

创建 `.env` 文件：

```bash
# 设备配置
DEFAULT_DEVICE=cuda
DEFAULT_DPI=200

# 表格检测配置
TABLE_DETECTION_CONF_THRESHOLD=0.1
TABLE_DETECTION_IOU_THRESHOLD=0.6

# 模型路径配置
MODEL_PATH_DOCLAYOUT_YOLO=models/DocLayout-YOLO-DocStructBench/doclayout_yolo_docstructbench_imgsz1024.pt
MODEL_PATH_SLANET_PLUS=models/SLANet_plus
MODEL_PATH_IMAGE_TO_HTML=models/StructTable-InternVL2-1B

# 输出配置
OUTPUT_MAX_IMAGE_WIDTH=3000
OUTPUT_MAX_IMAGE_HEIGHT=3000
```

### 代理配置（如果需要）

```bash
export http_proxy=http://127.0.0.1:7890
export https_proxy=http://127.0.0.1:7890
```

## 验证安装

```bash
# 检查安装
python -c "import src.main; print('安装成功！')"

# 运行测试（如果有测试文件）
pytest tests/

# 查看帮助信息
pdf-table-extract --help
```

## 常见问题

### 1. CUDA 相关错误

如果遇到 CUDA 相关错误，请：
- 确保安装了正确版本的 PyTorch
- 检查 CUDA 版本兼容性
- 可以使用 CPU 模式：`--device cpu`

### 2. 内存不足

如果遇到内存不足：
- 减小图像尺寸：`--max-size 2000`
- 使用 CPU 模式
- 分批处理文件

### 3. 模型加载失败

确保：
- 模型文件路径正确
- 模型文件完整下载
- 有足够的磁盘空间

### 4. 依赖冲突

如果遇到依赖冲突：
- 使用虚拟环境
- 更新 pip：`pip install --upgrade pip`
- 清理缓存：`pip cache purge`

## 卸载

```bash
pip uninstall pdf-table-extraction
```
