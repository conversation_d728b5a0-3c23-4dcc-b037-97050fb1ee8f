#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from setuptools import setup, find_packages
import os

# 读取README文件
def read_readme():
    readme_path = os.path.join(os.path.dirname(__file__), 'README.md')
    if os.path.exists(readme_path):
        with open(readme_path, 'r', encoding='utf-8') as f:
            return f.read()
    return "PDF表格提取工具 - 支持PDF、DOCX、DOC文件的表格检测和提取"

# 读取requirements.txt文件（如果存在）
def read_requirements():
    requirements_path = os.path.join(os.path.dirname(__file__), 'requirements.txt')
    if os.path.exists(requirements_path):
        with open(requirements_path, 'r', encoding='utf-8') as f:
            return [line.strip() for line in f if line.strip() and not line.startswith('#')]
    return []

# 基础依赖
base_requirements = [
    # 核心依赖
    'torch>=1.9.0',
    'torchvision>=0.10.0',
    'transformers>=4.20.0',
    'Pillow>=8.0.0',
    'numpy>=1.21.0',
    'tqdm>=4.60.0',
    
    # PDF处理
    'pypdfium2>=4.0.0',
    
    # 图像处理
    'opencv-python>=4.5.0',
    
    # 配置管理
    'python-dotenv>=0.19.0',
    
    # 日志
    'loguru>=0.6.0',
    
    # 数据处理
    'pandas>=1.3.0',
    
    # JSON处理
    'ujson>=4.0.0',
    
    # 进度条
    'rich>=10.0.0',
    
    # 类型检查
    'typing-extensions>=4.0.0',
]

# 可选依赖
optional_requirements = {
    'yolo': [
        'ultralytics>=8.0.0',
        'doclayout-yolo>=1.0.0',
    ],
    'mineru': [
        'mineru>=0.1.0',
    ],
    'document': [
        'python-docx>=0.8.11',
        'python-doc>=0.1.0',
    ],
    'dev': [
        'pytest>=6.0.0',
        'pytest-cov>=2.12.0',
        'black>=21.0.0',
        'flake8>=3.9.0',
        'mypy>=0.910',
        'pre-commit>=2.15.0',
    ],
    'all': [],  # 将在下面填充
}

# 填充 'all' 选项
all_optional = []
for deps in optional_requirements.values():
    if deps:  # 排除 'all' 本身
        all_optional.extend(deps)
optional_requirements['all'] = list(set(all_optional))

# 合并requirements.txt中的依赖（如果存在）
requirements_from_file = read_requirements()
if requirements_from_file:
    base_requirements.extend(requirements_from_file)

# 去重并排序
base_requirements = sorted(list(set(base_requirements)))

setup(
    name="pdf-table-extraction",
    version="1.0.0",
    author="PDF Table Extraction Team",
    author_email="<EMAIL>",
    description="PDF表格提取工具 - 支持PDF、DOCX、DOC文件的表格检测和提取",
    long_description=read_readme(),
    long_description_content_type="text/markdown",
    url="https://github.com/your-username/pdf-table-extraction",
    
    # 包配置
    packages=find_packages(),
    package_dir={'': '.'},
    
    # 包含数据文件
    include_package_data=True,
    package_data={
        'src': ['config/*.py'],
        '': ['*.md', '*.txt', '*.yml', '*.yaml'],
    },
    
    # 依赖
    install_requires=base_requirements,
    extras_require=optional_requirements,
    
    # Python版本要求
    python_requires=">=3.8",
    
    # 分类器
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Developers",
        "Intended Audience :: Science/Research",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Topic :: Scientific/Engineering :: Artificial Intelligence",
        "Topic :: Software Development :: Libraries :: Python Modules",
        "Topic :: Text Processing :: Markup :: HTML",
        "Topic :: Multimedia :: Graphics :: Graphics Conversion",
    ],
    
    # 关键词
    keywords="pdf table extraction ocr document processing ai machine-learning",
    
    # 项目URL
    project_urls={
        "Bug Reports": "https://github.com/your-username/pdf-table-extraction/issues",
        "Source": "https://github.com/your-username/pdf-table-extraction",
        "Documentation": "https://github.com/your-username/pdf-table-extraction/wiki",
    },
    
    # 命令行入口点
    entry_points={
        'console_scripts': [
            'pdf-table-extract=src.main:main',
            'table-extract=src.main:main',
        ],
    },
    
    # 数据文件
    data_files=[
        ('', ['start.sh']),
    ],
    
    # 压缩安全
    zip_safe=False,
    
    # 测试套件
    test_suite='tests',
    
    # 许可证
    license="MIT",
)
