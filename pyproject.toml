[build-system]
requires = ["setuptools>=45", "wheel", "setuptools_scm[toml]>=6.2"]
build-backend = "setuptools.build_meta"

[project]
name = "pdf-table-extraction"
version = "1.0.0"
description = "PDF表格提取工具 - 支持PDF、DOCX、DOC文件的表格检测和提取"
readme = "README.md"
license = {text = "MIT"}
authors = [
    {name = "PDF Table Extraction Team", email = "<EMAIL>"}
]
maintainers = [
    {name = "PDF Table Extraction Team", email = "<EMAIL>"}
]
keywords = ["pdf", "table", "extraction", "ocr", "document", "processing", "ai", "machine-learning"]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "Intended Audience :: Science/Research",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
    "Topic :: Software Development :: Libraries :: Python Modules",
    "Topic :: Text Processing :: Markup :: HTML",
    "Topic :: Multimedia :: Graphics :: Graphics Conversion",
]
requires-python = ">=3.8"
dependencies = [
    "torch>=1.9.0",
    "torchvision>=0.10.0",
    "transformers>=4.20.0",
    "Pillow>=8.0.0",
    "numpy>=1.21.0",
    "tqdm>=4.60.0",
    "pypdfium2>=4.0.0",
    "opencv-python>=4.5.0",
    "python-dotenv>=0.19.0",
    "loguru>=0.6.0",
    "pandas>=1.3.0",
    "ujson>=4.0.0",
    "rich>=10.0.0",
    "typing-extensions>=4.0.0",
]

[project.optional-dependencies]
yolo = [
    "ultralytics>=8.0.0",
    "doclayout-yolo>=1.0.0",
]
mineru = [
    "mineru>=0.1.0",
]
document = [
    "python-docx>=0.8.11",
    "python-doc>=0.1.0",
]
dev = [
    "pytest>=6.0.0",
    "pytest-cov>=2.12.0",
    "black>=21.0.0",
    "flake8>=3.9.0",
    "mypy>=0.910",
    "pre-commit>=2.15.0",
]
all = [
    "ultralytics>=8.0.0",
    "doclayout-yolo>=1.0.0",
    "mineru>=0.1.0",
    "python-docx>=0.8.11",
    "python-doc>=0.1.0",
]

[project.urls]
Homepage = "https://github.com/your-username/pdf-table-extraction"
Repository = "https://github.com/your-username/pdf-table-extraction"
Documentation = "https://github.com/your-username/pdf-table-extraction/wiki"
"Bug Reports" = "https://github.com/your-username/pdf-table-extraction/issues"

[project.scripts]
pdf-table-extract = "src.main:main"
table-extract = "src.main:main"

[tool.setuptools]
packages = ["src"]
include-package-data = true

[tool.setuptools.package-data]
src = ["config/*.py"]

[tool.black]
line-length = 88
target-version = ['py38']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
  | models
  | output
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["src"]

[tool.flake8]
max-line-length = 88
extend-ignore = ["E203", "W503"]
exclude = [
    ".git",
    "__pycache__",
    "build",
    "dist",
    "models",
    "output",
]

[tool.mypy]
python_version = "3.8"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
exclude = [
    "models/",
    "output/",
    "build/",
    "dist/",
]

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = [
    "--strict-markers",
    "--strict-config",
    "--verbose",
    "--cov=src",
    "--cov-report=term-missing",
    "--cov-report=html",
]

[tool.coverage.run]
source = ["src"]
omit = [
    "*/tests/*",
    "*/test_*",
    "*/__pycache__/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]
